/**
 * 用户认证上下文 - 管理用户登录状态和认证信息
 *
 * 功能：
 * - 用户登录/登出
 * - JWT Token管理
 * - 用户信息持久化存储
 * - 自动恢复登录状态
 *
 * 使用方式：
 * 1. 在App根组件包裹AuthProvider
 * 2. 在子组件中使用useAuth()获取认证状态
 */

import React, { createContext, useState, useContext, ReactNode } from 'react';
import { API_URL } from '../api/client';
import * as authStorage from './storage';
import { jwtDecode } from 'jwt-decode';

/**
 * JWT Token解码后的数据结构
 */
interface DecodedToken {
  user_id: number;
  username: string;
  email?: string;                             // 邮箱地址（可选）
  subscription_level: 'Free' | 'Pro' | 'Max';
  exp: number;
  iat: number;
}

/**
 * 用户信息接口
 */
interface User {
  username: string;                           // 用户名
  email?: string;                             // 邮箱地址（可选）
  subscription_level: 'Free' | 'Pro' | 'Max'; // 订阅等级
}

/**
 * 认证上下文接口定义
 */
interface AuthContextType {
  user: User | null;                          // 当前用户信息
  token: string | null;                       // JWT访问令牌
  login: (username: string, password: string) => Promise<boolean>;  // 登录函数
  logout: () => void;                         // 登出函数
  restoreUser: () => Promise<void>;           // 恢复用户状态函数
  updateToken: (newToken: string) => Promise<void>; // 更新token函数
  checkTokenExpiry: () => Promise<void>;      // 检查token过期状态
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * 认证上下文提供者组件
 * 管理全局认证状态，提供登录/登出功能
 */
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);

  /**
   * 用户登录函数
   * @param username 用户名
   * @param password 密码
   * @returns Promise<boolean> 登录是否成功
   */
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      // 调用后端登录API - 统一路径协议
      const response = await fetch(`${API_URL}/api/users/login/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password }),
      });
      const data = await response.json();

      if (response.ok && data.access) {
        // 存储JWT Token到本地存储
        await authStorage.storeToken(data.access);
        setToken(data.access);

        // 存储刷新令牌（如果有）
        if (data.refresh) {
          await authStorage.storeRefreshToken(data.refresh);
        }

        // 直接从token解码用户信息，避免存储延迟问题
        try {
          const decoded = jwtDecode<DecodedToken>(data.access);
          setUser({
            username: decoded.username,
            email: decoded.email || undefined,  // 安全处理email字段
            subscription_level: decoded.subscription_level
          });
          console.log('Login successful, subscription level:', decoded.subscription_level);
        } catch (decodeError) {
          console.error('Failed to decode token during login:', decodeError);
          // 如果解码失败，尝试从存储获取
          const userData = await authStorage.getUser();
          if (userData) {
            setUser({
              username: userData.username,
              email: userData.email || undefined,  // 安全处理email字段
              subscription_level: userData.subscription_level
            });
          }
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = async () => {
    try {
      // 停止会话检查，避免重复触发
      const { authManager } = require('../utils/authManager');
      authManager.stopSessionCheck();

      // 清除本地存储的认证信息
      await authStorage.removeToken();

      // 清除状态
      setUser(null);
      setToken(null);

      console.log('Logout completed successfully');
    } catch (error) {
      console.error('Error during logout:', error);
      // 即使出错也要清除状态
      setUser(null);
      setToken(null);
    }
  };

  const restoreUser = async () => {
    try {
      console.log('Attempting to restore user...');
      const userData = await authStorage.getUser();

      if (userData && userData.token) {
        console.log('Found stored user data, validating token...');

        // 验证token是否仍然有效
        try {
          const decoded = jwtDecode<DecodedToken>(userData.token);
          const currentTime = Date.now() / 1000;
          const timeUntilExpiry = decoded.exp - currentTime;

          console.log(`Token expires in ${Math.round(timeUntilExpiry / 60)} minutes`);

          // 检查token是否过期或即将过期（5分钟内）
          if (timeUntilExpiry > 300) { // 5分钟缓冲
            setUser({
              username: decoded.username,
              email: decoded.email || undefined,
              subscription_level: decoded.subscription_level
            });
            setToken(userData.token);
            console.log('User restored successfully, subscription level:', decoded.subscription_level);
          } else if (timeUntilExpiry > 0) {
            console.log('Token expires soon, attempting refresh...');
            // Token即将过期，尝试刷新
            await attemptTokenRefresh();
          } else {
            console.log('Stored token has expired, clearing user data');
            await authStorage.removeToken();
          }
        } catch (decodeError) {
          console.error('Failed to decode stored token:', decodeError);
          await authStorage.removeToken();
        }
      } else {
        console.log('No stored user data found');
      }
    } catch (error) {
      console.error('Error restoring user:', error);
      await authStorage.removeToken();
    }
  };

  /**
   * 更新token和用户信息
   * @param newToken 新的JWT token
   */
  const updateToken = async (newToken: string): Promise<void> => {
    try {
      // 存储新的token
      await authStorage.storeToken(newToken);
      setToken(newToken);

      // 直接从新token解码用户信息，避免存储延迟问题
      const decoded = jwtDecode<DecodedToken>(newToken);

      if (decoded && decoded.username && decoded.subscription_level) {
        setUser({
          username: decoded.username,
          email: decoded.email || undefined,  // 安全处理email字段
          subscription_level: decoded.subscription_level
        });
        console.log('Token updated successfully, new subscription level:', decoded.subscription_level);
      } else {
        console.warn('Token decode failed or missing required fields');
      }
    } catch (error) {
      console.error('Update token error:', error);
      // 如果解码失败，尝试从存储获取
      try {
        const userData = await authStorage.getUser();
        if (userData) {
          setUser({
            username: userData.username,
            email: userData.email || undefined,  // 安全处理email字段
            subscription_level: userData.subscription_level
          });
        }
      } catch (fallbackError) {
        console.error('Fallback user data retrieval failed:', fallbackError);
      }
    }
  };

  /**
   * 尝试刷新token
   */
  const attemptTokenRefresh = async (): Promise<boolean> => {
    try {
      console.log('Attempting token refresh...');

      // 直接调用token刷新逻辑
      const refreshToken = await authStorage.getRefreshToken();
      if (!refreshToken) {
        console.log('No refresh token available');
        await logout();
        return false;
      }

      const response = await fetch(`${API_URL}/api/users/token/refresh/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.access) {
          console.log('Token refresh successful');
          await updateToken(data.access);

          // 如果有新的刷新令牌，也要存储
          if (data.refresh) {
            await authStorage.storeRefreshToken(data.refresh);
          }

          return true;
        }
      }

      console.log('Token refresh failed');
      await logout();
      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      await logout();
      return false;
    }
  };

  /**
   * 检查token是否需要刷新
   */
  const checkTokenExpiry = async () => {
    if (!token) return;

    try {
      const decoded = jwtDecode<DecodedToken>(token);
      const currentTime = Date.now() / 1000;
      const timeUntilExpiry = decoded.exp - currentTime;

      // 如果token在10分钟内过期，尝试刷新
      if (timeUntilExpiry < 600 && timeUntilExpiry > 0) {
        console.log('Token expires soon, refreshing...');
        await attemptTokenRefresh();
      }
    } catch (error) {
      console.error('Error checking token expiry:', error);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      token,
      login,
      logout,
      restoreUser,
      updateToken,
      checkTokenExpiry
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};