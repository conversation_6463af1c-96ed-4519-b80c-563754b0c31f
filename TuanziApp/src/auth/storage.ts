import AsyncStorage from '@react-native-async-storage/async-storage';
import { jwtDecode } from 'jwt-decode';

// 统一的存储键名 - 确保与authManager.ts一致
const tokenKey = 'access_token';
const refreshTokenKey = 'refresh_token';
const userKey = 'user_data';
const legacyTokenKey = 'userToken'; // 兼容旧版本
const legacyRefreshKey = 'refreshToken'; // 兼容旧版本
const legacyUserKey = 'currentUser'; // 兼容旧版本

type DecodedToken = {
    user_id: number;
    username: string;
    email?: string;                             // 邮箱地址（可选）
    subscription_level: 'Free' | 'Pro' | 'Max';
};

/**
 * 存储访问令牌
 * @param token JWT访问令牌
 */
export const storeToken = async (token: string) => {
    try {
        // 使用统一的存储键
        await AsyncStorage.setItem(tokenKey, token);

        // 解码token并存储用户信息
        const decoded: DecodedToken = jwtDecode(token);
        const userData = {
            username: decoded.username,
            email: decoded.email || undefined,
            subscription_level: decoded.subscription_level,
            user_id: decoded.user_id,
            token: token
        };

        await AsyncStorage.setItem(userKey, JSON.stringify(userData));

        // 清理旧版本的存储键
        await AsyncStorage.multiRemove([legacyTokenKey, legacyUserKey]);

        console.log('Token stored successfully');
    } catch (error) {
        console.error('Error storing the auth token:', error);
        throw error;
    }
};

/**
 * 存储刷新令牌
 * @param refreshToken JWT刷新令牌
 */
export const storeRefreshToken = async (refreshToken: string) => {
    try {
        // 使用统一的存储键
        await AsyncStorage.setItem(refreshTokenKey, refreshToken);

        // 清理旧版本的存储键
        await AsyncStorage.removeItem(legacyRefreshKey);

        console.log('Refresh token stored successfully');
    } catch (error) {
        console.error('Error storing the refresh token:', error);
        throw error;
    }
};

/**
 * 获取访问令牌
 * @returns 访问令牌或null
 */
export const getToken = () => AsyncStorage.getItem(tokenKey);

/**
 * 获取刷新令牌
 * @returns 刷新令牌或null
 */
export const getRefreshToken = () => AsyncStorage.getItem(refreshTokenKey);

/**
 * 获取用户信息
 * @returns 用户信息对象或null
 */
export const getUser = async () => {
    try {
        // 首先尝试从存储的用户数据获取
        const userDataStr = await AsyncStorage.getItem(userKey);
        if (userDataStr) {
            const userData = JSON.parse(userDataStr);
            // 验证token是否仍然有效
            if (userData.token) {
                try {
                    const decoded: DecodedToken = jwtDecode(userData.token);
                    const currentTime = Date.now() / 1000;

                    if (decoded.exp > currentTime) {
                        return userData;
                    } else {
                        console.log('Stored token has expired');
                        await removeToken();
                        return null;
                    }
                } catch (decodeError) {
                    console.error('Failed to decode stored token:', decodeError);
                    await removeToken();
                    return null;
                }
            }
        }

        // 如果没有存储的用户数据，尝试从token重新构建
        const token = await getToken();
        if (!token) {
            // 检查是否有旧版本的数据需要迁移
            await migrateLegacyData();
            return null;
        }

        const decoded: DecodedToken = jwtDecode(token);
        const userData = {
            token,
            username: decoded.username,
            email: decoded.email || undefined,
            subscription_level: decoded.subscription_level,
            user_id: decoded.user_id
        };

        // 重新存储用户数据
        await AsyncStorage.setItem(userKey, JSON.stringify(userData));
        return userData;
    } catch (error) {
        console.error('Error getting user from token:', error);
        await removeToken();
        return null;
    }
};

/**
 * 迁移旧版本的数据
 */
const migrateLegacyData = async () => {
    try {
        const legacyToken = await AsyncStorage.getItem(legacyTokenKey);
        const legacyRefresh = await AsyncStorage.getItem(legacyRefreshKey);

        if (legacyToken) {
            console.log('Migrating legacy token data...');
            await storeToken(legacyToken);
        }

        if (legacyRefresh) {
            console.log('Migrating legacy refresh token...');
            await storeRefreshToken(legacyRefresh);
        }

        // 清理旧数据
        await AsyncStorage.multiRemove([legacyTokenKey, legacyRefreshKey, legacyUserKey]);
    } catch (error) {
        console.error('Error migrating legacy data:', error);
    }
};

/**
 * 移除所有认证相关的令牌和信息
 */
export const removeToken = async () => {
    try {
        // 确保清除所有可能存储认证信息的键（包括新旧版本）
        await AsyncStorage.multiRemove([
            tokenKey,
            refreshTokenKey,
            userKey,
            legacyTokenKey,
            legacyRefreshKey,
            legacyUserKey,
            'auth_token' // authManager可能使用的键
        ]);
        console.log('All auth tokens removed successfully');
    } catch (error) {
        console.error('Error removing auth tokens:', error);
    }
};
