/**
 * 房间时间轴模态框组件
 * 
 * 显示房间模板的环节排序与结构，支持房主编辑和其他参与者浏览
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { getRoomSteps, RoomStep, RoomStepsResponse, addStepToRoom } from '../api/eventApi';
import { theme } from '../styles/theme';
import { AddStepModal } from './AddStepModal';

interface RoomTimelineModalProps {
  roomCode: string;
  isVisible: boolean;
  onClose: () => void;
  isHost?: boolean;
}

export const RoomTimelineModal: React.FC<RoomTimelineModalProps> = ({
  roomCode,
  isVisible,
  onClose,
  isHost = false,
}) => {
  const [stepsData, setStepsData] = useState<RoomStepsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedStep, setSelectedStep] = useState<RoomStep | null>(null);
  const [isAddStepModalVisible, setIsAddStepModalVisible] = useState(false);
  const [insertAfterOrder, setInsertAfterOrder] = useState<number>(0);

  useEffect(() => {
    if (isVisible) {
      loadStepsData();
    }
  }, [isVisible, roomCode]);

  const loadStepsData = async () => {
    try {
      setLoading(true);
      console.log(`Loading steps data for room: ${roomCode}`);
      const data = await getRoomSteps(roomCode);
      console.log('Room steps data loaded successfully:', data);
      setStepsData(data);
    } catch (error) {
      console.error('Failed to load room steps:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        roomCode
      });
      Alert.alert(
        '加载失败',
        error instanceof Error ? error.message : '获取环节信息失败',
        [{ text: '确定' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleStepPress = (step: RoomStep) => {
    setSelectedStep(step);
  };

  const handleStepEdit = (step: RoomStep) => {
    if (!isHost) {
      Alert.alert('权限不足', '只有房主可以编辑环节');
      return;
    }

    Alert.alert(
      '编辑环节',
      `环节：${step.name}`,
      [
        { text: '取消', style: 'cancel' },
        { text: '修改', onPress: () => handleModifyStep(step) },
        { text: '删除', style: 'destructive', onPress: () => handleDeleteStep(step) },
      ]
    );
  };

  const handleModifyStep = (step: RoomStep) => {
    // TODO: 实现环节修改功能
    Alert.alert('功能开发中', '环节修改功能正在开发中');
  };

  const handleDeleteStep = (step: RoomStep) => {
    Alert.alert(
      '确认删除',
      `确定要删除环节"${step.name}"吗？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: () => {
            // TODO: 实现环节删除功能
            Alert.alert('功能开发中', '环节删除功能正在开发中');
          },
        },
      ]
    );
  };

  const handleInsertStep = (afterOrder: number) => {
    if (!isHost) {
      Alert.alert('权限不足', '只有房主可以插入环节');
      return;
    }

    // 设置插入位置并显示AddStepModal
    setInsertAfterOrder(afterOrder);
    setIsAddStepModalVisible(true);
  };

  const handleAddStep = async (stepData: {
    step_type: string;
    name?: string;
    duration?: number;
    configuration?: Record<string, any>;
  }) => {
    try {
      // 添加插入位置参数
      const stepDataWithPosition = {
        ...stepData,
        insert_after_order: insertAfterOrder
      };

      const result = await addStepToRoom(roomCode, stepDataWithPosition);
      Alert.alert('成功', result.message);

      // 成功后重新加载数据
      await loadStepsData();

      // 关闭AddStepModal
      setIsAddStepModalVisible(false);
    } catch (error) {
      console.error('添加环节失败:', error);
      Alert.alert('错误', error instanceof Error ? error.message : '添加环节失败');
      throw error; // 重新抛出错误，让Modal处理
    }
  };

  const getStepStatusIcon = (step: RoomStep) => {
    if (step.is_completed) {
      return '✅';
    } else if (step.is_current) {
      return '🔄';
    } else {
      return '⭕';
    }
  };

  const getStepTypeDisplay = (stepType: string) => {
    const typeMap: Record<string, string> = {
      'FREE_CHAT': '自由聊天',
      'DRAWING': '你画我猜',
      'UNDERCOVER': '谁是卧底',
      'PAUSE': '暂停休息',
      'CUSTOM': '自定义',
    };
    return typeMap[stepType] || stepType;
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
    }
    return `${seconds}秒`;
  };

  const renderTimeline = () => {
    if (!stepsData) return null;

    return (
      <ScrollView style={styles.timelineContainer}>
        {/* 在第一个环节前添加插入点 */}
        {isHost && stepsData.steps.length > 0 && (
          <TouchableOpacity
            style={styles.insertPointFirst}
            onPress={() => handleInsertStep(0)}
          >
            <Text style={styles.insertPointText}>+ 在开头插入环节</Text>
          </TouchableOpacity>
        )}

        {stepsData.steps.map((step, index) => (
          <View key={step.id}>
            {/* 环节节点 */}
            <View style={styles.stepContainer}>
              <View style={styles.timelineLeft}>
                <View style={[
                  styles.stepNode,
                  step.is_completed && styles.stepNodeCompleted,
                  step.is_current && styles.stepNodeCurrent,
                ]}>
                  <Text style={styles.stepNodeText}>
                    {getStepStatusIcon(step)}
                  </Text>
                </View>
                {index < stepsData.steps.length - 1 && (
                  <View style={styles.timelineLine} />
                )}
              </View>

              <TouchableOpacity
                style={[
                  styles.stepContent,
                  step.is_current && styles.stepContentCurrent,
                ]}
                onPress={() => handleStepPress(step)}
                onLongPress={() => handleStepEdit(step)}
              >
                <Text style={styles.stepName}>{step.name}</Text>
                <Text style={styles.stepType}>
                  {getStepTypeDisplay(step.step_type)}
                </Text>
                <Text style={styles.stepDuration}>
                  时长：{formatDuration(step.duration)}
                </Text>
                {step.is_completed && step.completed_at && (
                  <Text style={styles.stepCompleted}>
                    已完成：{new Date(step.completed_at).toLocaleTimeString()}
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {/* 插入点（仅房主可见） */}
            {isHost && index < stepsData.steps.length - 1 && (
              <TouchableOpacity
                style={styles.insertPoint}
                onPress={() => handleInsertStep(step.order)}
              >
                <Text style={styles.insertPointText}>+ 插入环节</Text>
              </TouchableOpacity>
            )}
          </View>
        ))}

        {/* 在最后一个环节后添加插入点 */}
        {isHost && stepsData.steps.length > 0 && (
          <TouchableOpacity
            style={styles.insertPointLast}
            onPress={() => handleInsertStep(stepsData.steps[stepsData.steps.length - 1].order)}
          >
            <Text style={styles.insertPointText}>+ 在末尾添加环节</Text>
          </TouchableOpacity>
        )}

        {/* 如果没有环节，显示添加第一个环节的按钮 */}
        {isHost && stepsData.steps.length === 0 && (
          <TouchableOpacity
            style={styles.insertPointEmpty}
            onPress={() => handleInsertStep(0)}
          >
            <Text style={styles.insertPointText}>+ 添加第一个环节</Text>
          </TouchableOpacity>
        )}
      </ScrollView>
    );
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>房间时间轴</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>完成</Text>
          </TouchableOpacity>
        </View>

        {stepsData && (
          <View style={styles.statusBar}>
            <Text style={styles.statusText}>
              房间状态：{stepsData.room_status}
            </Text>
            <Text style={styles.statusText}>
              进度：{stepsData.current_step_order}/{stepsData.total_steps}
            </Text>
          </View>
        )}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>加载中...</Text>
          </View>
        ) : (
          renderTimeline()
        )}

        {isHost && (
          <View style={styles.hostTip}>
            <Text style={styles.hostTipText}>
              💡 长按环节可编辑，点击"+"可插入新环节
            </Text>
          </View>
        )}
      </View>

      {/* 环节详情模态框 */}
      {selectedStep && (
        <Modal
          visible={!!selectedStep}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setSelectedStep(null)}
        >
          <TouchableOpacity
            style={styles.detailOverlay}
            activeOpacity={1}
            onPress={() => setSelectedStep(null)}
          >
            <View style={styles.detailContainer}>
              <Text style={styles.detailTitle}>{selectedStep.name}</Text>
              <Text style={styles.detailInfo}>
                类型：{getStepTypeDisplay(selectedStep.step_type)}
              </Text>
              <Text style={styles.detailInfo}>
                时长：{formatDuration(selectedStep.duration)}
              </Text>
              <Text style={styles.detailInfo}>
                顺序：第 {selectedStep.order} 个环节
              </Text>
              <Text style={styles.detailInfo}>
                状态：{selectedStep.is_completed ? '已完成' : 
                       selectedStep.is_current ? '进行中' : '未开始'}
              </Text>
              {selectedStep.completed_at && (
                <Text style={styles.detailInfo}>
                  完成时间：{new Date(selectedStep.completed_at).toLocaleString()}
                </Text>
              )}
            </View>
          </TouchableOpacity>
        </Modal>
      )}

      {/* 添加环节模态框 */}
      <AddStepModal
        isVisible={isAddStepModalVisible}
        onClose={() => setIsAddStepModalVisible(false)}
        onAddStep={handleAddStep}
        roomCode={roomCode}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#f8f9fa',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: theme.colors.primary,
    borderRadius: 6,
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#f0f8ff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statusText: {
    fontSize: 14,
    color: '#666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  timelineContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  stepContainer: {
    flexDirection: 'row',
    marginVertical: 8,
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: 16,
  },
  stepNode: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ccc',
  },
  stepNodeCompleted: {
    backgroundColor: '#d4edda',
    borderColor: '#28a745',
  },
  stepNodeCurrent: {
    backgroundColor: '#cce5ff',
    borderColor: '#007bff',
  },
  stepNodeText: {
    fontSize: 16,
  },
  timelineLine: {
    width: 2,
    height: 60,
    backgroundColor: '#e0e0e0',
    marginTop: 4,
  },
  stepContent: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  stepContentCurrent: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196f3',
  },
  stepName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  stepType: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  stepDuration: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  stepCompleted: {
    fontSize: 12,
    color: '#28a745',
    fontStyle: 'italic',
  },
  insertPoint: {
    alignSelf: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#e8f5e8',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#28a745',
    borderStyle: 'dashed',
    marginVertical: 4,
  },
  insertPointText: {
    fontSize: 12,
    color: '#28a745',
    fontWeight: '600',
  },
  insertPointFirst: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e8f5e8',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#28a745',
    borderStyle: 'dashed',
    marginBottom: 12,
    marginTop: 8,
  },
  insertPointLast: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e8f5e8',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#28a745',
    borderStyle: 'dashed',
    marginTop: 12,
    marginBottom: 8,
  },
  insertPointEmpty: {
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#007bff',
    borderStyle: 'dashed',
    marginVertical: 20,
  },
  hostTip: {
    backgroundColor: '#fff3cd',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  hostTipText: {
    fontSize: 12,
    color: '#856404',
    textAlign: 'center',
  },
  detailOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  detailContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    margin: 20,
    maxWidth: 300,
    width: '80%',
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
    textAlign: 'center',
  },
  detailInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
});
