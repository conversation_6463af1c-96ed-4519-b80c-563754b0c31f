/**
 * 团子APP主导航器
 *
 * 按照用户设计图重新实现的导航系统：
 * 1. 主页面：左下角亮橙色到右上角暖黄色的渐变背景
 * 2. 上导航栏：透明背景，左侧用户名+权限等级，右侧设置入口
 * 3. 下导航栏：透明背景，三个按钮（环节编辑器、房间、日历）使用SVG图标
 * 4. 自动登录状态处理：任何登录问题都自动退回登录界面
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Dimensions,
  Alert,
} from 'react-native';
import { createStackNavigator } from '@react-navigation/stack';
import LinearGradient from 'react-native-linear-gradient';
import Svg, { Path } from 'react-native-svg';

import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { RootStackParamList } from '../types';
import { theme } from '../styles/theme';
import {
  useDeviceInfo,
  navigationSizes,
  getBackButtonPosition,
  responsiveSize,
  logDeviceInfo
} from '../utils/deviceUtils';

// 导入页面组件
import { HomeScreen } from '../screens/HomeScreen';
import { TemplateListScreen } from '../screens/EventDesigner/TemplateListScreen';
import { CalendarScreen } from '../screens/CalendarScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import { CreateRoomScreen } from '../screens/CreateRoomScreen';
import { RoomScreen } from '../screens/RoomScreen';
import { ScheduleRoomScreen } from '../screens/ScheduleRoomScreen';
import { SubscriptionScreen } from '../screens/SubscriptionScreen';
import { CreateTemplateScreen } from '../screens/EventDesigner/CreateTemplateScreen';
import { TemplateDetailScreen } from '../screens/EventDesigner/TemplateDetailScreen';
import { AddStepScreen } from '../screens/EventDesigner/AddStepScreen';
import { EditStepScreen } from '../screens/EventDesigner/EditStepScreen';

const Stack = createStackNavigator<RootStackParamList>();
const { width, height } = Dimensions.get('window');

// SVG图标组件
const EventDesignerIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"
      fill={color}
    />
    <Path
      d="M19 15L19.5 17L21 17.5L19.5 18L19 20L18.5 18L17 17.5L18.5 17L19 15Z"
      fill={color}
    />
    <Path
      d="M5 15L5.5 17L7 17.5L5.5 18L5 20L4.5 18L3 17.5L4.5 17L5 15Z"
      fill={color}
    />
  </Svg>
);

const RoomIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"
      fill={color}
    />
  </Svg>
);

const CalendarIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M19 3H18V1H16V3H8V1H6V3H5C3.89 3 3.01 3.9 3.01 5L3 19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V8H19V19ZM7 10H12V15H7V10Z"
      fill={color}
    />
  </Svg>
);

const SettingsIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M12 15.5C13.933 15.5 15.5 13.933 15.5 12C15.5 10.067 13.933 8.5 12 8.5C10.067 8.5 8.5 10.067 8.5 12C8.5 13.933 10.067 15.5 12 15.5Z"
      fill={color}
    />
    <Path
      d="M19.43 12.98C19.47 12.66 19.5 12.34 19.5 12C19.5 11.66 19.47 11.34 19.43 11.02L21.54 9.37C21.73 9.22 21.78 8.95 21.66 8.73L19.66 5.27C19.54 5.05 19.27 4.97 19.05 5.05L16.56 6.05C16.04 5.65 15.48 5.32 14.87 5.07L14.49 2.42C14.46 2.18 14.25 2 14 2H10C9.75 2 9.54 2.18 9.51 2.42L9.13 5.07C8.52 5.32 7.96 5.66 7.44 6.05L4.95 5.05C4.72 4.96 4.46 5.05 4.34 5.27L2.34 8.73C2.21 8.95 2.27 9.22 2.46 9.37L4.57 11.02C4.53 11.34 4.5 11.67 4.5 12C4.5 12.33 4.53 12.66 4.57 12.98L2.46 14.63C2.27 14.78 2.21 15.05 2.34 15.27L4.34 18.73C4.46 18.95 4.73 19.03 4.95 18.95L7.44 17.95C7.96 18.35 8.52 18.68 9.13 18.93L9.51 21.58C9.54 21.82 9.75 22 10 22H14C14.25 22 14.46 21.82 14.49 21.58L14.87 18.93C15.48 18.68 16.04 18.34 16.56 17.95L19.05 18.95C19.28 19.04 19.54 18.95 19.66 18.73L21.66 15.27C21.78 15.05 21.73 14.78 21.54 14.63L19.43 12.98Z"
      fill={color}
    />
  </Svg>
);

type TabType = 'eventDesigner' | 'room' | 'calendar';

/**
 * 主页面容器组件
 * 包含渐变背景、上下导航栏和内容区域
 * 优化：使用页面缓存和条件渲染提高切换性能
 */
const MainContainer = () => {
  const { user, logout } = useAuth();
  const { subscriptionInfo } = useSubscription();
  const [activeTab, setActiveTab] = useState<TabType>('room');
  const [showSettings, setShowSettings] = useState(false);

  // 页面缓存状态 - 避免重复渲染
  const [loadedTabs, setLoadedTabs] = useState<Set<TabType>>(new Set(['room']));

  // 获取设备信息用于布局适配
  const deviceInfo = useDeviceInfo();
  const backButtonPosition = getBackButtonPosition();

  // 初始化时输出设备信息（开发调试用）
  useEffect(() => {
    if (__DEV__) {
      logDeviceInfo();
    }
  }, []);

  // 监听登录状态，如果出现问题自动退出
  useEffect(() => {
    if (!user) {
      // 如果用户信息丢失，自动退出登录
      logout();
    }
  }, [user, logout]);

  // 优化的tab切换处理 - 预加载页面
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setLoadedTabs(prev => new Set([...prev, tab]));
  };

  const renderContent = () => {
    return (
      <>
        {/* 使用条件渲染和display控制，避免重复创建组件 */}
        <View style={[styles.tabContent, activeTab === 'eventDesigner' ? styles.tabContentActive : styles.tabContentHidden]}>
          {loadedTabs.has('eventDesigner') && <TemplateListScreen />}
        </View>
        <View style={[styles.tabContent, activeTab === 'room' ? styles.tabContentActive : styles.tabContentHidden]}>
          {loadedTabs.has('room') && <HomeScreen />}
        </View>
        <View style={[styles.tabContent, activeTab === 'calendar' ? styles.tabContentActive : styles.tabContentHidden]}>
          {loadedTabs.has('calendar') && <CalendarScreen />}
        </View>
      </>
    );
  };

  const getSubscriptionBadge = () => {
    const level = subscriptionInfo?.current_level || 'Free';
    const colors = {
      Free: theme.colors.subscription.free,
      Pro: theme.colors.subscription.pro,
      Max: theme.colors.subscription.max,
    };
    return { text: level, color: colors[level as keyof typeof colors] };
  };

  const handleSettingsPress = () => {
    setShowSettings(true);
  };

  if (showSettings) {
    return (
      <View style={styles.container}>
        <StatusBar
          barStyle={deviceInfo.isIOS ? "dark-content" : "light-content"}
          backgroundColor="transparent"
          translucent
        />
        <SettingsScreen />
        <TouchableOpacity
          style={[styles.backButton, {
            top: backButtonPosition.top,
            left: backButtonPosition.left,
          }]}
          onPress={() => setShowSettings(false)}
        >
          <Text style={styles.backButtonText}>← 返回</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={deviceInfo.isIOS ? "dark-content" : "light-content"}
        backgroundColor="transparent"
        translucent
      />

      {/* 多层渐变背景 - 更现代的设计 */}
      <LinearGradient
        colors={[
          theme.colors.primary,
          theme.colors.secondary,
          theme.colors.tertiary,
          theme.colors.gradientEnd
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBackground}
      />

      {/* 装饰性渐变层 - 增加深度感 */}
      <LinearGradient
        colors={['rgba(255, 255, 255, 0.1)', 'transparent', 'rgba(255, 255, 255, 0.05)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.decorativeGradient}
      />

      <SafeAreaView style={[styles.safeArea, { paddingTop: deviceInfo.safeAreaTop }]}>
        {/* 上导航栏 - 玻璃态设计 */}
        <View style={[styles.topNavBar, {
          height: navigationSizes.topNavHeight(),
          paddingHorizontal: navigationSizes.contentMargin(),
        }]}>
          {/* 玻璃态背景 */}
          <View style={styles.glassBackground} />

          <View style={styles.topNavLeft}>
            <View style={styles.userInfoContainer}>
              <Text style={[styles.userName, {
                fontSize: responsiveSize.fontSize(theme.typography.fontSize.lg),
              }]}>{user?.username || '用户'}</Text>
              <View style={styles.userSubtitle}>
                <Text style={[styles.welcomeText, {
                  fontSize: responsiveSize.fontSize(theme.typography.fontSize.xs),
                }]}>欢迎回来</Text>
              </View>
            </View>
            <View style={[styles.subscriptionBadge, {
              backgroundColor: getSubscriptionBadge().color,
              ...theme.shadows.sm,
            }]}>
              <Text style={[styles.subscriptionText, {
                fontSize: responsiveSize.fontSize(theme.typography.fontSize.xs),
              }]}>{getSubscriptionBadge().text}</Text>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.settingsButton, {
              padding: responsiveSize.spacing(theme.spacing.sm),
              ...theme.shadows.md,
            }]}
            onPress={handleSettingsPress}
            testID="settings-button"
          >
            <SettingsIcon color={theme.colors.white} size={navigationSizes.navButtonSize()} />
          </TouchableOpacity>
        </View>

        {/* 内容区域 - 现代卡片设计 */}
        <View style={[styles.contentArea, {
          marginHorizontal: navigationSizes.contentMargin(),
          marginBottom: navigationSizes.contentMargin(),
          ...theme.shadows.xl,
        }]}>
          {/* 内容区域装饰边框 */}
          <View style={styles.contentBorder} />
          {renderContent()}
        </View>

        {/* 下导航栏 - 现代浮动设计 */}
        <View style={[styles.bottomNavBar, {
          height: navigationSizes.bottomNavHeight(),
          paddingHorizontal: navigationSizes.contentMargin(),
          paddingBottom: deviceInfo.safeAreaBottom,
        }]}>
          {/* 导航栏玻璃态背景 */}
          <View style={styles.navGlassBackground} />

          {/* 导航栏装饰线 */}
          <View style={styles.navTopBorder} />

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'eventDesigner' && styles.navButtonActive, {
              paddingVertical: responsiveSize.spacing(theme.spacing.sm),
              paddingHorizontal: responsiveSize.spacing(theme.spacing.md),
            }]}
            onPress={() => handleTabChange('eventDesigner')}
          >
            <View style={[styles.iconContainer, activeTab === 'eventDesigner' && styles.iconContainerActive]}>
              <EventDesignerIcon
                color={activeTab === 'eventDesigner' ? theme.colors.white : theme.colors.white + '80'}
                size={navigationSizes.navButtonSize()}
              />
            </View>
            <Text style={[
              styles.navButtonText,
              activeTab === 'eventDesigner' && styles.navButtonTextActive,
              { fontSize: responsiveSize.fontSize(theme.typography.fontSize.xs) }
            ]}>
              环节编辑器
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'room' && styles.navButtonActive, {
              paddingVertical: responsiveSize.spacing(theme.spacing.sm),
              paddingHorizontal: responsiveSize.spacing(theme.spacing.md),
            }]}
            onPress={() => handleTabChange('room')}
          >
            <View style={[styles.iconContainer, activeTab === 'room' && styles.iconContainerActive]}>
              <RoomIcon
                color={activeTab === 'room' ? theme.colors.white : theme.colors.white + '80'}
                size={navigationSizes.navButtonSize()}
              />
            </View>
            <Text style={[
              styles.navButtonText,
              activeTab === 'room' && styles.navButtonTextActive,
              { fontSize: responsiveSize.fontSize(theme.typography.fontSize.xs) }
            ]}>
              房间
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, activeTab === 'calendar' && styles.navButtonActive, {
              paddingVertical: responsiveSize.spacing(theme.spacing.sm),
              paddingHorizontal: responsiveSize.spacing(theme.spacing.md),
            }]}
            onPress={() => handleTabChange('calendar')}
          >
            <View style={[styles.iconContainer, activeTab === 'calendar' && styles.iconContainerActive]}>
              <CalendarIcon
                color={activeTab === 'calendar' ? theme.colors.white : theme.colors.white + '80'}
                size={navigationSizes.navButtonSize()}
              />
            </View>
            <Text style={[
              styles.navButtonText,
              activeTab === 'calendar' && styles.navButtonTextActive,
              { fontSize: responsiveSize.fontSize(theme.typography.fontSize.xs) }
            ]}>
              日历
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

// 主导航器（包含模态页面）
export const AppNavigator = () => (
  <Stack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <Stack.Screen
      name="MainTabs"
      component={MainContainer}
    />
    <Stack.Screen
      name="CreateRoom"
      component={CreateRoomScreen}
      options={({ route }) => ({
        headerShown: true,
        title: route.params?.mode === 'select' ? '选择模板' : '创建房间',
        presentation: 'modal',
      })}
    />
    <Stack.Screen
      name="Room"
      component={RoomScreen}
      options={({ route }) => ({
        headerShown: true,
        title: `房间: ${route.params.room.room_code}`,
        presentation: 'card',
      })}
    />
    <Stack.Screen
      name="CreateTemplate"
      component={CreateTemplateScreen}
      options={{
        headerShown: true,
        title: '创建新模板',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="TemplateDetail"
      component={TemplateDetailScreen}
      options={{
        headerShown: true,
        title: '模板详情',
        presentation: 'card',
      }}
    />
    <Stack.Screen
      name="AddStep"
      component={AddStepScreen}
      options={{
        headerShown: true,
        title: '添加新步骤',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="EditStep"
      component={EditStepScreen}
      options={{
        headerShown: true,
        title: '编辑步骤',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="Subscription"
      component={SubscriptionScreen}
      options={{
        headerShown: true,
        title: '订阅管理',
        presentation: 'modal',
      }}
    />
    <Stack.Screen
      name="ScheduleRoom"
      component={ScheduleRoomScreen}
      options={{
        headerShown: true,
        title: '预约房间',
        presentation: 'modal',
      }}
    />
  </Stack.Navigator>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradientBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  decorativeGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  safeArea: {
    flex: 1,
  },

  // 上导航栏
  topNavBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    backgroundColor: 'transparent',
    position: 'relative',
    overflow: 'hidden',
  },
  glassBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    borderRadius: theme.borderRadius.lg,
  },
  topNavLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
    zIndex: 1,
  },
  userInfoContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  userSubtitle: {
    marginTop: 2,
  },
  welcomeText: {
    color: theme.colors.white + 'CC',
    fontWeight: theme.typography.fontWeight.normal,
  },
  userName: {
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.white,
  },
  subscriptionBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
  },
  subscriptionText: {
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.white,
  },
  settingsButton: {
    borderRadius: theme.borderRadius.full,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    zIndex: 1,
  },

  // 内容区域
  contentArea: {
    flex: 1,
    borderRadius: theme.borderRadius['2xl'],
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    overflow: 'hidden',
    position: 'relative',
  },
  contentBorder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 3,
    background: `linear-gradient(90deg, ${theme.colors.primary}, ${theme.colors.secondary}, ${theme.colors.tertiary})`,
    borderTopLeftRadius: theme.borderRadius['2xl'],
    borderTopRightRadius: theme.borderRadius['2xl'],
  },

  // Tab内容样式 - 优化页面切换性能
  tabContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  tabContentActive: {
    zIndex: 1,
  },
  tabContentHidden: {
    zIndex: 0,
    opacity: 0,
    pointerEvents: 'none',
  },

  // 下导航栏
  bottomNavBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    backgroundColor: 'transparent',
    position: 'relative',
    overflow: 'hidden',
  },
  navGlassBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    backdropFilter: 'blur(15px)',
  },
  navTopBorder: {
    position: 'absolute',
    top: 0,
    left: theme.spacing.lg,
    right: theme.spacing.lg,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  navButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: theme.borderRadius.lg,
    minWidth: 80,
    zIndex: 1,
  },
  navButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    ...theme.shadows.md,
  },
  iconContainer: {
    padding: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    backgroundColor: 'transparent',
  },
  iconContainerActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    ...theme.shadows.sm,
  },
  navButtonText: {
    fontWeight: theme.typography.fontWeight.medium,
    color: theme.colors.white + '80',
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
  navButtonTextActive: {
    color: theme.colors.white,
    fontWeight: theme.typography.fontWeight.bold,
  },

  // 返回按钮（设置页面）
  backButton: {
    position: 'absolute',
    padding: theme.spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: theme.borderRadius.md,
    zIndex: 1000,
  },
  backButtonText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    fontWeight: theme.typography.fontWeight.medium,
  },
});
