import React, { useState, useEffect, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { useAuth } from '../auth/AuthContext';
import { AppNavigator } from './AppNavigator';
import { AuthNavigator } from './AuthNavigator';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { authManager } from '../utils/authManager';
import { useNavigation } from '@react-navigation/native';

export const RootNavigator = () => {
  const { user, restoreUser, logout } = useAuth();
  const [isReady, setIsReady] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const navigationRef = useRef(null);

  useEffect(() => {
    const prepare = async () => {
      try {
        setIsInitializing(true);
        // 优化：并行处理用户恢复和其他初始化任务
        await Promise.race([
          restoreUser(),
          // 设置最大等待时间，避免长时间阻塞
          new Promise(resolve => setTimeout(resolve, 3000))
        ]);
      } catch (error) {
        console.error('Navigation initialization error:', error);
      } finally {
        setIsInitializing(false);
        setIsReady(true);
      }
    };
    prepare();
  }, [restoreUser]);

  // 设置authManager的认证过期回调
  useEffect(() => {
    if (isReady) {
      // 设置认证过期回调，直接调用AuthContext的logout
      authManager.setOnAuthExpired(() => {
        logout();
      });

      // 只有在用户已登录时才启动会话状态检查
      if (user) {
        authManager.startSessionCheck();
      } else {
        authManager.stopSessionCheck();
      }

      return () => {
        // 组件卸载时停止会话检查
        authManager.stopSessionCheck();
      };
    }
  }, [isReady, user, logout]);

  if (!isReady) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B35" />
        <Text style={styles.loadingText}>
          {isInitializing ? '正在初始化...' : '加载中...'}
        </Text>
      </View>
    );
  }

  return (
    <NavigationContainer ref={navigationRef}>
      {user ? <AppNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
});
