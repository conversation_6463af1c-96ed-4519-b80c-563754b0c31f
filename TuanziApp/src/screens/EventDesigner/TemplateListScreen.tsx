/**
 * 环节设计器页面组件
 *
 * 这是用户管理和创建环节模板的主要界面。
 * 按照新的设计规范，提供清晰的模板管理体验。
 *
 * 主要功能：
 * 1. 顶部新建模板按钮：醒目的创建入口
 * 2. 模板列表：显示用户已创建的所有模板
 * 3. 系统模板：显示系统提供的默认模板
 * 4. 模板操作：查看、编辑、删除模板
 *
 * 界面特性：
 * - 顶部固定的新建按钮
 * - 现代化的卡片式模板展示
 * - 清晰的视觉层次
 * - 流畅的动画效果
 * - 响应式布局适配不同屏幕
 *
 * 技术实现：
 * - 基于React Native的函数式组件
 * - 集成认证和订阅上下文
 * - 使用FlatList优化长列表性能
 * - 支持下拉刷新和加载状态
 * - 完整的错误处理和用户反馈
 */

import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
  RefreshControl
} from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { getEventTemplates } from '../../api/eventApi';
import { EventTemplate, RootStackParamList } from '../../types';
import { Card, Button, Badge } from '../../components';
import { theme } from '../../styles/theme';

type NavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

export const TemplateListScreen = () => {
    const { token, user } = useAuth();
    const { subscriptionInfo } = useSubscription();
    const navigation = useNavigation<NavigationProp>();
    const [templates, setTemplates] = useState<EventTemplate[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);

    // 动画值
    const fadeAnim = new Animated.Value(0);
    const slideAnim = new Animated.Value(30);

    React.useEffect(() => {
        // 页面加载动画
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const fetchTemplates = useCallback(async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getEventTemplates(token);
            setTemplates(data);
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '无法加载环节模板。');
        } finally {
            setIsLoading(false);
        }
    }, [token]);

    const onRefresh = useCallback(async () => {
        setRefreshing(true);
        await fetchTemplates();
        setRefreshing(false);
    }, [fetchTemplates]);

    useFocusEffect(useCallback(() => { fetchTemplates(); }, [fetchTemplates]));

    const handleCreateTemplate = () => {
        // 检查订阅权限
        if (!subscriptionInfo || subscriptionInfo.current_level === 'Free') {
            Alert.alert(
                '需要订阅',
                '创建自定义模板需要Pro订阅。是否前往订阅页面？',
                [
                    { text: '取消', style: 'cancel' },
                    { text: '前往订阅', onPress: () => navigation.navigate('Subscription') }
                ]
            );
            return;
        }
        navigation.navigate('CreateTemplate');
    };

    const renderTemplateItem = ({ item, index }: { item: EventTemplate; index: number }) => (
        <Animated.View
            style={[
                styles.templateItemContainer,
                {
                    opacity: fadeAnim,
                    transform: [
                        {
                            translateY: slideAnim.interpolate({
                                inputRange: [0, 30],
                                outputRange: [0, 30],
                            }),
                        },
                    ],
                },
            ]}
        >
            <Card
                style={styles.templateCard}
                variant="elevated"
                onPress={() => navigation.navigate('TemplateDetail', { templateId: item.id })}
            >
                <View style={styles.templateHeader}>
                    <View style={styles.templateIcon}>
                        <Text style={styles.templateEmoji}>
                            {item.is_system ? '⚙️' : '🎨'}
                        </Text>
                    </View>
                    <View style={styles.templateInfo}>
                        <Text style={styles.templateName}>{item.name}</Text>
                        <Text style={styles.templateDescription} numberOfLines={2}>
                            {item.description}
                        </Text>
                    </View>
                    {item.is_system && (
                        <Badge
                            text="系统"
                            variant="secondary"
                            size="xs"
                        />
                    )}
                </View>
                <View style={styles.templateFooter}>
                    <Text style={styles.templateSteps}>
                        {item.steps?.length || 0} 个环节
                    </Text>
                    <Text style={styles.templateArrow}>→</Text>
                </View>
            </Card>
        </Animated.View>
    );

    if (isLoading && !refreshing) {
        return (
            <View style={styles.container}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                    <Text style={styles.loadingText}>加载模板中...</Text>
                </View>
            </View>
        );
    }

    // 分离系统模板和用户模板
    const systemTemplates = templates.filter(t => t.is_system);
    const userTemplates = templates.filter(t => !t.is_system);

    return (
        <View style={styles.container}>
            {/* 顶部新建模板按钮 */}
            <Animated.View
                style={[
                    styles.headerSection,
                    {
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }]
                    }
                ]}
            >
                <Card style={styles.createTemplateCard} variant="vibrant">
                    <TouchableOpacity
                        style={styles.createTemplateButton}
                        onPress={handleCreateTemplate}
                    >
                        <View style={styles.createTemplateContent}>
                            <Text style={styles.createTemplateIcon}>❓</Text>
                            <Text style={styles.createTemplateText}>新建模板</Text>
                        </View>
                    </TouchableOpacity>
                </Card>
            </Animated.View>

            {/* 模板列表 */}
            <FlatList
                data={[...systemTemplates, ...userTemplates]}
                renderItem={renderTemplateItem}
                keyExtractor={(item) => `${item.is_system ? 'system' : 'user'}_${item.id}`}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={onRefresh}
                        colors={[theme.colors.primary]}
                        tintColor={theme.colors.primary}
                    />
                }
                ListHeaderComponent={
                    <Animated.View
                        style={[
                            styles.listHeader,
                            { opacity: fadeAnim }
                        ]}
                    >
                        <Text style={styles.sectionTitle}>
                            {systemTemplates.length > 0 && '📋 系统模板'}
                        </Text>
                    </Animated.View>
                }
                ListEmptyComponent={
                    <Animated.View
                        style={[
                            styles.emptyContainer,
                            { opacity: fadeAnim }
                        ]}
                    >
                        <Text style={styles.emptyEmoji}>🎨</Text>
                        <Text style={styles.emptyTitle}>还没有模板</Text>
                        <Text style={styles.emptyDescription}>
                            点击上方按钮创建您的第一个环节模板
                        </Text>
                    </Animated.View>
                }
                ItemSeparatorComponent={() => <View style={styles.separator} />}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: 'transparent',
    },

    // 加载状态
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: theme.spacing.md,
    },
    loadingText: {
        fontSize: theme.typography.fontSize.base,
        color: theme.colors.textSecondary,
        fontWeight: theme.typography.fontWeight.medium,
    },

    // 头部区域
    headerSection: {
        paddingHorizontal: theme.spacing.lg,
        paddingTop: theme.spacing.lg,
        paddingBottom: theme.spacing.md,
    },

    // 新建模板卡片
    createTemplateCard: {
        backgroundColor: theme.colors.primary + '15',
        borderColor: theme.colors.primary + '30',
        borderWidth: 2,
    },
    createTemplateButton: {
        padding: theme.spacing.lg,
    },
    createTemplateContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: theme.spacing.md,
    },
    createTemplateIcon: {
        fontSize: 28,
    },
    createTemplateText: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.bold,
        color: theme.colors.primary,
    },

    // 列表区域
    listContainer: {
        paddingHorizontal: theme.spacing.lg,
        paddingBottom: theme.spacing['2xl'],
    },
    listHeader: {
        marginBottom: theme.spacing.md,
    },
    sectionTitle: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.bold,
        color: theme.colors.textPrimary,
        marginBottom: theme.spacing.sm,
    },

    // 模板项
    templateItemContainer: {
        marginBottom: theme.spacing.md,
    },
    templateCard: {
        backgroundColor: theme.colors.surface,
        borderWidth: 1,
        borderColor: theme.colors.border,
    },
    templateHeader: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: theme.spacing.md,
        marginBottom: theme.spacing.sm,
    },
    templateIcon: {
        width: 40,
        height: 40,
        borderRadius: theme.borderRadius.lg,
        backgroundColor: theme.colors.primary + '15',
        justifyContent: 'center',
        alignItems: 'center',
    },
    templateEmoji: {
        fontSize: 20,
    },
    templateInfo: {
        flex: 1,
        gap: theme.spacing.xs,
    },
    templateName: {
        fontSize: theme.typography.fontSize.lg,
        fontWeight: theme.typography.fontWeight.semibold,
        color: theme.colors.textPrimary,
    },
    templateDescription: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.textSecondary,
        lineHeight: 20,
    },
    templateFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: theme.spacing.sm,
        paddingTop: theme.spacing.sm,
        borderTopWidth: 1,
        borderTopColor: theme.colors.border,
    },
    templateSteps: {
        fontSize: theme.typography.fontSize.xs,
        color: theme.colors.textTertiary,
        fontWeight: theme.typography.fontWeight.medium,
    },
    templateArrow: {
        fontSize: theme.typography.fontSize.lg,
        color: theme.colors.primary,
        fontWeight: theme.typography.fontWeight.bold,
    },

    // 分隔符
    separator: {
        height: theme.spacing.sm,
    },

    // 空状态
    emptyContainer: {
        alignItems: 'center',
        paddingVertical: theme.spacing['3xl'],
        gap: theme.spacing.md,
    },
    emptyEmoji: {
        fontSize: 64,
        marginBottom: theme.spacing.md,
    },
    emptyTitle: {
        fontSize: theme.typography.fontSize.xl,
        fontWeight: theme.typography.fontWeight.bold,
        color: theme.colors.textPrimary,
        marginBottom: theme.spacing.xs,
    },
    emptyDescription: {
        fontSize: theme.typography.fontSize.base,
        color: theme.colors.textSecondary,
        textAlign: 'center',
        lineHeight: 24,
        paddingHorizontal: theme.spacing.xl,
    },
});
