/**
 * 团子APP房间页面组件
 *
 * 这是用户进行房间相关操作的主要界面，专注于房间的创建和加入功能。
 * 按照新的设计规范，提供简洁直观的房间管理体验。
 *
 * 主要功能：
 * 1. 醒目的视觉展示：大面积的图案展示区域
 * 2. 创建房间：快速创建新的游戏房间
 * 3. 加入房间：通过房间代码加入现有房间
 * 4. 用户状态：显示当前用户信息和订阅状态
 *
 * 界面特性：
 * - 大面积的视觉图案作为主要展示区域
 * - 底部操作区域包含创建和加入功能
 * - 现代化的卡片式设计
 * - 流畅的动画效果
 * - 响应式布局适配不同屏幕
 *
 * 技术实现：
 * - 基于React Native的函数式组件
 * - 集成认证和订阅上下文
 * - 使用Animated API实现动画效果
 * - 支持导航参数传递
 * - 完整的错误处理和用户反馈
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions
} from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { API_URL } from '../api/client';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { Card, Button, Badge } from '../components';
import { theme } from '../styles/theme';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const HomeScreen = () => {
  const { user, token } = useAuth();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // 动画值
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  const patternAnim = new Animated.Value(0);

  useEffect(() => {
    // 页面加载动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // 图案动画循环
    const patternAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(patternAnim, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        }),
        Animated.timing(patternAnim, {
          toValue: 0,
          duration: 3000,
          useNativeDriver: true,
        }),
      ])
    );
    patternAnimation.start();

    return () => patternAnimation.stop();
  }, []);

  const handleJoinRoom = async () => {
    if (!roomCodeInput.trim()) {
      Alert.alert('提示', '请输入房间代码');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api/rooms/join/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ room_code: roomCodeInput.trim() }),
      });
      const data = await response.json();
      if (response.ok) {
        navigation.navigate('Room', { room: data.room });
      } else {
        Alert.alert('加入失败', data.error || '无法加入房间。');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('错误', '加入房间时发生错误。');
    }
  };

  const handleCreateRoom = () => {
    navigation.navigate('CreateRoom');
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 主要图案展示区域 */}
        <Animated.View
          style={[
            styles.patternSection,
            {
              opacity: fadeAnim,
              transform: [
                {
                  scale: patternAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [1, 1.05],
                  }),
                },
              ],
            }
          ]}
        >
          <View style={styles.patternContainer}>
            <Text style={styles.patternEmoji}>🎮</Text>
            <Text style={styles.patternTitle}>一个游戏</Text>
            <Text style={styles.patternSubtitle}>开始你的团建之旅</Text>
          </View>
        </Animated.View>

        {/* 底部操作区域 */}
        <Animated.View style={[styles.actionSection, { opacity: fadeAnim }]}>
          {/* 创建房间按钮 */}
          <Card style={styles.createRoomCard} variant="elevated">
            <TouchableOpacity
              style={styles.createRoomButton}
              onPress={handleCreateRoom}
            >
              <View style={styles.createRoomContent}>
                <Text style={styles.createRoomIcon}>🏠</Text>
                <Text style={styles.createRoomText}>创建房间</Text>
              </View>
            </TouchableOpacity>
          </Card>

          {/* 加入房间区域 */}
          <View style={styles.joinRoomSection}>
            <View style={styles.joinInputContainer}>
              <TextInput
                style={styles.joinInput}
                placeholder="房间号"
                value={roomCodeInput}
                onChangeText={setRoomCodeInput}
                autoCapitalize="characters"
                maxLength={6}
                placeholderTextColor={theme.colors.textTertiary}
              />
              <Button
                title="加入"
                onPress={handleJoinRoom}
                size="medium"
                disabled={!roomCodeInput.trim()}
                variant="primary"
              />
            </View>
          </View>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: theme.spacing['2xl'],
  },

  // 图案展示区域
  patternSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing['3xl'],
    minHeight: 300,
  },
  patternContainer: {
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius['2xl'],
    padding: theme.spacing['2xl'],
    shadowColor: theme.colors.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  patternEmoji: {
    fontSize: 80,
    marginBottom: theme.spacing.lg,
  },
  patternTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  patternSubtitle: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },

  // 底部操作区域
  actionSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
    gap: theme.spacing.lg,
  },

  // 创建房间卡片
  createRoomCard: {
    backgroundColor: theme.colors.primary + '10',
    borderColor: theme.colors.primary + '30',
    borderWidth: 2,
  },
  createRoomButton: {
    padding: theme.spacing.lg,
  },
  createRoomContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.md,
  },
  createRoomIcon: {
    fontSize: 32,
  },
  createRoomText: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.primary,
  },

  // 加入房间区域
  joinRoomSection: {
    gap: theme.spacing.md,
  },
  joinInputContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'center',
  },
  joinInput: {
    flex: 1,
    height: 48,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
    textAlign: 'center',
    fontWeight: theme.typography.fontWeight.medium,
  },
});
