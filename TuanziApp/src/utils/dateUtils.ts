/**
 * 日期和时区处理工具函数
 * 
 * 提供统一的日期处理方法，确保时区显示正确
 */

/**
 * 获取本地时区的今天日期（不包含时间）
 * @returns 本地时区的今天日期
 */
export const getLocalToday = (): Date => {
  const now = new Date();
  // 创建一个只包含年月日的本地日期，时间设为00:00:00
  return new Date(now.getFullYear(), now.getMonth(), now.getDate());
};

/**
 * 获取本地时区的当前时间
 * @returns 本地时区的当前时间
 */
export const getLocalNow = (): Date => {
  return new Date();
};

/**
 * 检查给定日期是否是今天（基于本地时区）
 * @param date 要检查的日期
 * @returns 是否是今天
 */
export const isToday = (date: Date): boolean => {
  const today = getLocalToday();
  const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  return checkDate.getTime() === today.getTime();
};

/**
 * 检查给定日期是否是过去的日期（基于本地时区）
 * @param date 要检查的日期
 * @returns 是否是过去的日期
 */
export const isPastDate = (date: Date): boolean => {
  const today = getLocalToday();
  const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  return checkDate.getTime() < today.getTime();
};

/**
 * 检查给定日期是否是未来的日期（基于本地时区）
 * @param date 要检查的日期
 * @returns 是否是未来的日期
 */
export const isFutureDate = (date: Date): boolean => {
  const today = getLocalToday();
  const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  return checkDate.getTime() > today.getTime();
};

/**
 * 格式化日期为本地时区的字符串
 * @param date 要格式化的日期
 * @param options 格式化选项
 * @returns 格式化后的日期字符串
 */
export const formatLocalDate = (
  date: Date, 
  options: {
    includeYear?: boolean;
    includeTime?: boolean;
    includeSeconds?: boolean;
  } = {}
): string => {
  const { includeYear = true, includeTime = false, includeSeconds = false } = options;
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  let dateStr = includeYear ? `${year}年${month}月${day}日` : `${month}月${day}日`;
  
  if (includeTime) {
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    let timeStr = `${hours}:${minutes}`;
    
    if (includeSeconds) {
      const seconds = String(date.getSeconds()).padStart(2, '0');
      timeStr += `:${seconds}`;
    }
    
    dateStr += ` ${timeStr}`;
  }
  
  return dateStr;
};

/**
 * 格式化时间为本地时区的字符串（仅时分）
 * @param date 要格式化的日期
 * @returns 格式化后的时间字符串
 */
export const formatLocalTime = (date: Date): string => {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * 将UTC时间字符串转换为本地时间
 * @param utcString UTC时间字符串（ISO格式）
 * @returns 本地时间的Date对象
 */
export const utcToLocal = (utcString: string): Date => {
  return new Date(utcString);
};

/**
 * 将本地时间转换为UTC时间字符串
 * @param localDate 本地时间的Date对象
 * @returns UTC时间字符串（ISO格式）
 */
export const localToUtc = (localDate: Date): string => {
  return localDate.toISOString();
};

/**
 * 获取日期的本地时区字符串表示（YYYY-MM-DD格式）
 * @param date 日期对象
 * @returns 本地时区的日期字符串
 */
export const getLocalDateString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 比较两个日期是否是同一天（基于本地时区）
 * @param date1 第一个日期
 * @param date2 第二个日期
 * @returns 是否是同一天
 */
export const isSameLocalDate = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

/**
 * 获取本地时区的月份开始日期
 * @param year 年份
 * @param month 月份（1-12）
 * @returns 月份开始日期
 */
export const getLocalMonthStart = (year: number, month: number): Date => {
  return new Date(year, month - 1, 1);
};

/**
 * 获取本地时区的月份结束日期
 * @param year 年份
 * @param month 月份（1-12）
 * @returns 月份结束日期
 */
export const getLocalMonthEnd = (year: number, month: number): Date => {
  return new Date(year, month, 0);
};

/**
 * 检查预约时间是否有效（基于本地时区）
 * @param scheduledTime 预约时间
 * @param minMinutesAhead 最小提前时间（分钟），默认30分钟
 * @returns 是否有效
 */
export const isValidScheduleTime = (scheduledTime: Date, minMinutesAhead: number = 30): boolean => {
  const now = getLocalNow();
  const minTime = new Date(now.getTime() + minMinutesAhead * 60 * 1000);
  return scheduledTime >= minTime;
};

/**
 * 获取时区偏移信息
 * @returns 时区偏移信息
 */
export const getTimezoneInfo = () => {
  const now = new Date();
  const timezoneOffset = now.getTimezoneOffset(); // 分钟
  const offsetHours = Math.abs(Math.floor(timezoneOffset / 60));
  const offsetMinutes = Math.abs(timezoneOffset % 60);
  const offsetSign = timezoneOffset <= 0 ? '+' : '-';
  
  return {
    offset: timezoneOffset,
    offsetString: `UTC${offsetSign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`,
    name: Intl.DateTimeFormat().resolvedOptions().timeZone
  };
};

/**
 * 调试用：打印日期时间信息
 * @param date 日期对象
 * @param label 标签
 */
export const debugDateTime = (date: Date, label: string = 'Date') => {
  const timezone = getTimezoneInfo();
  console.log(`[${label}] Local: ${formatLocalDate(date, { includeTime: true, includeSeconds: true })}`);
  console.log(`[${label}] UTC: ${date.toISOString()}`);
  console.log(`[${label}] Timezone: ${timezone.offsetString} (${timezone.name})`);
};
