/**
 * 设备适配工具
 * 
 * 用于处理不同设备的屏幕尺寸、安全区域、状态栏高度等适配问题
 * 确保导航栏在各种移动设备上都有合适的布局和间距
 */

import { Dimensions, Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// 获取设备尺寸
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * 设备类型枚举
 */
export enum DeviceType {
  PHONE = 'phone',
  TABLET = 'tablet',
  SMALL_PHONE = 'small_phone',
  LARGE_PHONE = 'large_phone'
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  width: number;
  height: number;
  type: DeviceType;
  isIOS: boolean;
  isAndroid: boolean;
  hasNotch: boolean;
  statusBarHeight: number;
  safeAreaTop: number;
  safeAreaBottom: number;
}

/**
 * 获取设备类型
 */
export const getDeviceType = (): DeviceType => {
  const aspectRatio = screenHeight / screenWidth;
  
  if (screenWidth >= 768) {
    return DeviceType.TABLET;
  }
  
  if (screenWidth < 350) {
    return DeviceType.SMALL_PHONE;
  }
  
  if (screenWidth > 414 || aspectRatio > 2.1) {
    return DeviceType.LARGE_PHONE;
  }
  
  return DeviceType.PHONE;
};

/**
 * 检测是否有刘海屏或异形屏
 */
export const hasNotch = (): boolean => {
  if (Platform.OS === 'ios') {
    // iOS设备刘海屏检测 - 使用屏幕高度比例判断
    const aspectRatio = screenHeight / screenWidth;
    return aspectRatio > 2.0; // iPhone X系列及以上的屏幕比例
  } else {
    // Android设备异形屏检测
    const statusBarHeight = StatusBar.currentHeight || 0;
    return statusBarHeight > 24;
  }
};

/**
 * 获取状态栏高度
 */
export const getStatusBarHeightSafe = (): number => {
  if (Platform.OS === 'ios') {
    // iOS状态栏高度根据设备类型判断
    const deviceHasNotch = hasNotch();
    return deviceHasNotch ? 44 : 20;
  } else {
    return StatusBar.currentHeight || 24;
  }
};

/**
 * 获取安全区域顶部高度
 */
export const getSafeAreaTop = (): number => {
  const statusBarHeight = getStatusBarHeightSafe();
  const deviceHasNotch = hasNotch();
  
  if (Platform.OS === 'ios') {
    return deviceHasNotch ? statusBarHeight : statusBarHeight + 10;
  } else {
    return deviceHasNotch ? statusBarHeight + 10 : statusBarHeight + 5;
  }
};

/**
 * 获取安全区域底部高度
 */
export const getSafeAreaBottom = (): number => {
  const deviceHasNotch = hasNotch();
  const deviceType = getDeviceType();
  
  if (Platform.OS === 'ios') {
    if (deviceHasNotch) {
      return 34; // iPhone X系列底部安全区域
    }
    return 0;
  } else {
    if (deviceType === DeviceType.LARGE_PHONE && deviceHasNotch) {
      return 20; // Android大屏设备底部导航栏
    }
    return 0;
  }
};

/**
 * 获取完整的设备信息
 */
export const getDeviceInfo = (): DeviceInfo => {
  const deviceType = getDeviceType();
  const deviceHasNotch = hasNotch();
  const statusBarHeight = getStatusBarHeightSafe();
  const safeAreaTop = getSafeAreaTop();
  const safeAreaBottom = getSafeAreaBottom();
  
  return {
    width: screenWidth,
    height: screenHeight,
    type: deviceType,
    isIOS: Platform.OS === 'ios',
    isAndroid: Platform.OS === 'android',
    hasNotch: deviceHasNotch,
    statusBarHeight,
    safeAreaTop,
    safeAreaBottom,
  };
};

/**
 * 响应式尺寸计算
 */
export const responsiveSize = {
  /**
   * 基于屏幕宽度的响应式尺寸
   */
  width: (percentage: number): number => {
    return (screenWidth * percentage) / 100;
  },
  
  /**
   * 基于屏幕高度的响应式尺寸
   */
  height: (percentage: number): number => {
    return (screenHeight * percentage) / 100;
  },
  
  /**
   * 基于设备类型的响应式字体大小
   */
  fontSize: (baseSize: number): number => {
    const deviceType = getDeviceType();
    
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        return baseSize * 0.9;
      case DeviceType.LARGE_PHONE:
        return baseSize * 1.1;
      case DeviceType.TABLET:
        return baseSize * 1.3;
      default:
        return baseSize;
    }
  },
  
  /**
   * 基于设备类型的响应式间距
   */
  spacing: (baseSpacing: number): number => {
    const deviceType = getDeviceType();
    
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        return baseSpacing * 0.8;
      case DeviceType.LARGE_PHONE:
        return baseSpacing * 1.1;
      case DeviceType.TABLET:
        return baseSpacing * 1.5;
      default:
        return baseSpacing;
    }
  },
};

/**
 * 导航栏适配尺寸
 */
export const navigationSizes = {
  /**
   * 上导航栏高度
   */
  topNavHeight: (): number => {
    const deviceType = getDeviceType();
    const baseHeight = 60;
    
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        return baseHeight * 0.9;
      case DeviceType.LARGE_PHONE:
        return baseHeight * 1.1;
      case DeviceType.TABLET:
        return baseHeight * 1.2;
      default:
        return baseHeight;
    }
  },
  
  /**
   * 下导航栏高度
   */
  bottomNavHeight: (): number => {
    const deviceType = getDeviceType();
    const safeAreaBottom = getSafeAreaBottom();
    const baseHeight = 80;
    
    let adaptedHeight;
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        adaptedHeight = baseHeight * 0.9;
        break;
      case DeviceType.LARGE_PHONE:
        adaptedHeight = baseHeight * 1.1;
        break;
      case DeviceType.TABLET:
        adaptedHeight = baseHeight * 1.2;
        break;
      default:
        adaptedHeight = baseHeight;
    }
    
    return adaptedHeight + safeAreaBottom;
  },
  
  /**
   * 导航栏按钮尺寸
   */
  navButtonSize: (): number => {
    const deviceType = getDeviceType();
    const baseSize = 28;
    
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        return baseSize * 0.9;
      case DeviceType.LARGE_PHONE:
        return baseSize * 1.1;
      case DeviceType.TABLET:
        return baseSize * 1.3;
      default:
        return baseSize;
    }
  },
  
  /**
   * 内容区域边距
   */
  contentMargin: (): number => {
    const deviceType = getDeviceType();
    const baseMargin = 16;
    
    switch (deviceType) {
      case DeviceType.SMALL_PHONE:
        return baseMargin * 0.8;
      case DeviceType.LARGE_PHONE:
        return baseMargin * 1.1;
      case DeviceType.TABLET:
        return baseMargin * 1.5;
      default:
        return baseMargin;
    }
  },
};

/**
 * 获取返回按钮的安全位置
 */
export const getBackButtonPosition = (): { top: number; left: number } => {
  const safeAreaTop = getSafeAreaTop();
  const contentMargin = navigationSizes.contentMargin();
  
  return {
    top: safeAreaTop + 10, // 状态栏高度 + 额外间距
    left: contentMargin,
  };
};

/**
 * 调试信息输出
 */
export const logDeviceInfo = (): void => {
  const deviceInfo = getDeviceInfo();
  console.log('=== 设备信息 ===');
  console.log(`设备类型: ${deviceInfo.type}`);
  console.log(`屏幕尺寸: ${deviceInfo.width} x ${deviceInfo.height}`);
  console.log(`平台: ${deviceInfo.isIOS ? 'iOS' : 'Android'}`);
  console.log(`是否有刘海屏: ${deviceInfo.hasNotch ? '是' : '否'}`);
  console.log(`状态栏高度: ${deviceInfo.statusBarHeight}px`);
  console.log(`安全区域顶部: ${deviceInfo.safeAreaTop}px`);
  console.log(`安全区域底部: ${deviceInfo.safeAreaBottom}px`);
  console.log(`上导航栏高度: ${navigationSizes.topNavHeight()}px`);
  console.log(`下导航栏高度: ${navigationSizes.bottomNavHeight()}px`);
  console.log('================');
};

/**
 * Hook版本的设备信息获取器
 * 使用react-native-safe-area-context提供更准确的安全区域信息
 */
export const useDeviceInfo = (): DeviceInfo => {
  const insets = useSafeAreaInsets();
  const deviceType = getDeviceType();
  const deviceHasNotch = hasNotch();

  return {
    width: screenWidth,
    height: screenHeight,
    type: deviceType,
    isIOS: Platform.OS === 'ios',
    isAndroid: Platform.OS === 'android',
    hasNotch: deviceHasNotch,
    statusBarHeight: insets.top,
    safeAreaTop: insets.top,
    safeAreaBottom: insets.bottom,
  };
};
