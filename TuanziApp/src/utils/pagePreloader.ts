/**
 * 页面预加载工具
 * 
 * 用于优化页面切换性能，通过预加载和缓存机制
 * 减少页面切换时的延迟和加载竞争问题
 */

import { useEffect, useRef, useState } from 'react';

/**
 * 页面预加载状态管理
 */
export interface PagePreloadState {
  isLoaded: boolean;
  isLoading: boolean;
  error?: Error;
  lastLoadTime?: number;
}

/**
 * 页面预加载管理器
 */
class PagePreloadManager {
  private loadedPages = new Map<string, PagePreloadState>();
  private loadingPromises = new Map<string, Promise<void>>();
  private maxCacheAge = 5 * 60 * 1000; // 5分钟缓存过期时间

  /**
   * 检查页面是否已加载
   */
  isPageLoaded(pageId: string): boolean {
    const state = this.loadedPages.get(pageId);
    if (!state) return false;
    
    // 检查缓存是否过期
    if (state.lastLoadTime && Date.now() - state.lastLoadTime > this.maxCacheAge) {
      this.loadedPages.delete(pageId);
      return false;
    }
    
    return state.isLoaded && !state.error;
  }

  /**
   * 获取页面加载状态
   */
  getPageState(pageId: string): PagePreloadState {
    return this.loadedPages.get(pageId) || {
      isLoaded: false,
      isLoading: false,
    };
  }

  /**
   * 标记页面为已加载
   */
  markPageLoaded(pageId: string): void {
    this.loadedPages.set(pageId, {
      isLoaded: true,
      isLoading: false,
      lastLoadTime: Date.now(),
    });
    this.loadingPromises.delete(pageId);
  }

  /**
   * 标记页面加载失败
   */
  markPageError(pageId: string, error: Error): void {
    this.loadedPages.set(pageId, {
      isLoaded: false,
      isLoading: false,
      error,
      lastLoadTime: Date.now(),
    });
    this.loadingPromises.delete(pageId);
  }

  /**
   * 开始预加载页面
   */
  async preloadPage(pageId: string, loadFunction: () => Promise<void>): Promise<void> {
    // 如果已经在加载中，返回现有的Promise
    if (this.loadingPromises.has(pageId)) {
      return this.loadingPromises.get(pageId)!;
    }

    // 如果已经加载过且未过期，直接返回
    if (this.isPageLoaded(pageId)) {
      return Promise.resolve();
    }

    // 标记为加载中
    this.loadedPages.set(pageId, {
      isLoaded: false,
      isLoading: true,
    });

    // 创建加载Promise
    const loadPromise = loadFunction()
      .then(() => {
        this.markPageLoaded(pageId);
      })
      .catch((error) => {
        this.markPageError(pageId, error);
        throw error;
      });

    this.loadingPromises.set(pageId, loadPromise);
    return loadPromise;
  }

  /**
   * 清除页面缓存
   */
  clearPageCache(pageId?: string): void {
    if (pageId) {
      this.loadedPages.delete(pageId);
      this.loadingPromises.delete(pageId);
    } else {
      this.loadedPages.clear();
      this.loadingPromises.clear();
    }
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache(): void {
    const now = Date.now();
    for (const [pageId, state] of this.loadedPages.entries()) {
      if (state.lastLoadTime && now - state.lastLoadTime > this.maxCacheAge) {
        this.loadedPages.delete(pageId);
      }
    }
  }
}

// 全局预加载管理器实例
export const pagePreloadManager = new PagePreloadManager();

/**
 * 页面预加载Hook
 * 
 * @param pageId 页面唯一标识
 * @param loadFunction 页面加载函数
 * @param autoLoad 是否自动开始加载
 */
export const usePagePreload = (
  pageId: string,
  loadFunction: () => Promise<void>,
  autoLoad: boolean = false
) => {
  const [state, setState] = useState<PagePreloadState>(() => 
    pagePreloadManager.getPageState(pageId)
  );
  const loadFunctionRef = useRef(loadFunction);

  // 更新loadFunction引用
  useEffect(() => {
    loadFunctionRef.current = loadFunction;
  }, [loadFunction]);

  // 监听状态变化
  useEffect(() => {
    const checkState = () => {
      const newState = pagePreloadManager.getPageState(pageId);
      setState(newState);
    };

    const interval = setInterval(checkState, 100);
    return () => clearInterval(interval);
  }, [pageId]);

  // 自动加载
  useEffect(() => {
    if (autoLoad && !state.isLoaded && !state.isLoading) {
      preload();
    }
  }, [autoLoad, state.isLoaded, state.isLoading]);

  const preload = async () => {
    try {
      await pagePreloadManager.preloadPage(pageId, loadFunctionRef.current);
    } catch (error) {
      console.error(`Page preload failed for ${pageId}:`, error);
    }
  };

  const clearCache = () => {
    pagePreloadManager.clearPageCache(pageId);
    setState({
      isLoaded: false,
      isLoading: false,
    });
  };

  return {
    ...state,
    preload,
    clearCache,
    isReady: state.isLoaded && !state.error,
  };
};

/**
 * 批量页面预加载Hook
 * 
 * @param pages 页面配置数组
 */
export const useBatchPagePreload = (
  pages: Array<{
    pageId: string;
    loadFunction: () => Promise<void>;
    priority?: number; // 优先级，数字越小优先级越高
  }>
) => {
  const [overallState, setOverallState] = useState({
    loadedCount: 0,
    totalCount: pages.length,
    isAllLoaded: false,
    errors: [] as Array<{ pageId: string; error: Error }>,
  });

  useEffect(() => {
    const loadPages = async () => {
      // 按优先级排序
      const sortedPages = [...pages].sort((a, b) => (a.priority || 0) - (b.priority || 0));
      
      let loadedCount = 0;
      const errors: Array<{ pageId: string; error: Error }> = [];

      // 并行加载所有页面
      await Promise.allSettled(
        sortedPages.map(async (page) => {
          try {
            await pagePreloadManager.preloadPage(page.pageId, page.loadFunction);
            loadedCount++;
          } catch (error) {
            errors.push({ pageId: page.pageId, error: error as Error });
          }
        })
      );

      setOverallState({
        loadedCount,
        totalCount: pages.length,
        isAllLoaded: loadedCount === pages.length,
        errors,
      });
    };

    if (pages.length > 0) {
      loadPages();
    }
  }, [pages]);

  return overallState;
};

/**
 * 定期清理过期缓存
 */
setInterval(() => {
  pagePreloadManager.cleanupExpiredCache();
}, 60000); // 每分钟清理一次
