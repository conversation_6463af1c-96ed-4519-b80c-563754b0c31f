"""
API模块

提供团子APP的所有API视图，按功能模块组织：
- 用户认证API
- 房间管理API  
- 预约系统API
- 模板系统API
- 房间环节API
- 订阅系统API
- 签到系统API

所有API都遵循RESTful设计原则和统一的响应格式。
"""

from .auth_views import *
from .room_views import *
from .reservation_views import *
from .template_views import *
from .step_views import *
from .subscription_views import *
from .checkin_views import *
from .base_views import *

__all__ = [
    # 基础视图
    'APIRootView',
    'HealthCheckView',
    
    # 认证视图
    'RegisterView',
    'CustomTokenObtainPairView',
    
    # 房间视图
    'RoomCreateView',
    'JoinRoomView',
    'RoomDetailView',
    
    # 预约视图
    'ScheduleRoomView',
    'ReservationDetailView',
    'CalendarDataView',
    
    # 模板视图
    'RoomTemplateListView',
    
    # 环节视图
    'AddStepToRoomView',
    'InsertStepToRoomView',
    'DeleteStepFromRoomView',
    'RoomStepsView',
    
    # 订阅视图
    'SubscriptionManagementView',
    
    # 签到视图
    'CheckInView',
]
