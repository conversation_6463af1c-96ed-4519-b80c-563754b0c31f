"""
用户认证API视图

提供用户认证相关的API端点，包括：
- 用户注册
- 用户登录
- JWT令牌管理
- 用户信息获取

集成新的用户服务层和异常处理系统。
"""

import logging
from rest_framework import generics, status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

from .base_views import BaseAPIView
from ..user.services import UserService
from ..user.models import User
from ..serializers import UserSerializer, CustomTokenObtainPairSerializer
from ..logging.logger import get_logger, performance_monitor, log_exceptions
from ..exceptions import (
    UserAlreadyExistsException,
    InvalidCredentialsException,
    ValidationException
)

logger = get_logger(__name__)


class RegisterView(BaseAPIView, generics.CreateAPIView):
    """
    用户注册API视图

    提供用户注册功能，包括：
    - 用户名和密码验证
    - 重复用户检查
    - 自动JWT令牌生成
    - 订阅等级初始化
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [AllowAny]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    @performance_monitor('api.user_register')
    @log_exceptions('api')
    def post(self, request, *args, **kwargs):
        """
        用户注册端点

        Args:
            request: HTTP请求，包含用户注册信息

        Returns:
            Response: 注册结果和JWT令牌
        """
        serializer = self.get_serializer(data=request.data)
        
        if not serializer.is_valid():
            return self.error_response(
                message="注册信息验证失败",
                error_code="VALIDATION_ERROR",
                details=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 使用用户服务创建用户
            user = self.user_service.create_user(
                username=serializer.validated_data['username'],
                password=serializer.validated_data['password'],
                email=serializer.validated_data.get('email'),
                first_name=serializer.validated_data.get('first_name', ''),
                last_name=serializer.validated_data.get('last_name', '')
            )

            # 生成JWT令牌
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # 记录注册成功
            logger.info(
                f"用户注册成功",
                extra={
                    'user_id': user.id,
                    'username': user.username,
                    'subscription_level': user.subscription_level,
                    'ip_address': request.META.get('REMOTE_ADDR')
                }
            )

            response_data = {
                'user': UserSerializer(user).data,
                'tokens': {
                    'access': access_token,
                    'refresh': refresh_token
                }
            }

            return self.success_response(
                data=response_data,
                message="注册成功",
                status_code=status.HTTP_201_CREATED
            )

        except UserAlreadyExistsException as e:
            return self.error_response(
                message=str(e),
                error_code=e.error_code,
                details=e.details,
                status_code=status.HTTP_409_CONFLICT
            )
        except ValidationException as e:
            return self.error_response(
                message=str(e),
                error_code=e.error_code,
                details=e.details,
                status_code=status.HTTP_400_BAD_REQUEST
            )


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    自定义JWT令牌获取视图

    提供用户登录功能，包括：
    - 用户名密码验证
    - JWT令牌生成
    - 用户信息返回
    - 登录日志记录
    """
    serializer_class = CustomTokenObtainPairSerializer

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    @performance_monitor('api.user_login')
    @log_exceptions('api')
    def post(self, request, *args, **kwargs):
        """
        用户登录端点

        Args:
            request: HTTP请求，包含登录凭据

        Returns:
            Response: 登录结果和JWT令牌
        """
        try:
            # 调用父类方法获取令牌
            response = super().post(request, *args, **kwargs)
            
            if response.status_code == status.HTTP_200_OK:
                # 获取用户信息
                username = request.data.get('username')
                user = self.user_service.get_user_by_username(username)
                
                if user:
                    # 更新最后登录时间
                    self.user_service.update_last_login(user)
                    
                    # 添加用户信息到响应
                    response.data['user'] = UserSerializer(user).data
                    
                    # 记录登录成功
                    logger.info(
                        f"用户登录成功",
                        extra={
                            'user_id': user.id,
                            'username': user.username,
                            'subscription_level': user.subscription_level,
                            'ip_address': request.META.get('REMOTE_ADDR')
                        }
                    )

            return response

        except InvalidCredentialsException as e:
            logger.warning(
                f"登录失败: 无效凭据",
                extra={
                    'username': request.data.get('username'),
                    'ip_address': request.META.get('REMOTE_ADDR')
                }
            )
            return Response({
                'success': False,
                'message': str(e),
                'error_code': e.error_code,
                'details': e.details
            }, status=status.HTTP_401_UNAUTHORIZED)


class UserProfileView(BaseAPIView):
    """
    用户信息API视图

    提供用户信息管理功能，包括：
    - 获取用户信息
    - 更新用户信息
    - 订阅状态查询
    """
    permission_classes = [IsAuthenticated]

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.user_service = UserService()

    @performance_monitor('api.user_profile_get')
    def get(self, request, *args, **kwargs):
        """
        获取用户信息

        Returns:
            Response: 用户详细信息
        """
        user = request.user
        
        # 获取用户统计信息
        user_stats = self.user_service.get_user_statistics(user)
        
        response_data = {
            'user': UserSerializer(user).data,
            'statistics': user_stats,
            'subscription': {
                'level': user.subscription_level,
                'limits': self.user_service.get_subscription_limits(user)
            }
        }

        return self.success_response(
            data=response_data,
            message="获取用户信息成功"
        )

    @performance_monitor('api.user_profile_patch')
    @log_exceptions('api')
    def patch(self, request, *args, **kwargs):
        """
        更新用户信息

        Args:
            request: HTTP请求，包含更新的用户信息

        Returns:
            Response: 更新结果
        """
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True)
        
        if not serializer.is_valid():
            return self.error_response(
                message="用户信息验证失败",
                error_code="VALIDATION_ERROR",
                details=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 使用用户服务更新用户信息
            updated_user = self.user_service.update_user(
                user,
                **serializer.validated_data
            )

            logger.info(
                f"用户信息更新成功",
                extra={
                    'user_id': user.id,
                    'updated_fields': list(serializer.validated_data.keys())
                }
            )

            return self.success_response(
                data=UserSerializer(updated_user).data,
                message="用户信息更新成功"
            )

        except ValidationException as e:
            return self.error_response(
                message=str(e),
                error_code=e.error_code,
                details=e.details,
                status_code=status.HTTP_400_BAD_REQUEST
            )
