"""
基础API视图

提供通用的API视图和工具类，包括：
- API根视图
- 健康检查视图
- 通用响应格式
- 错误处理装饰器

所有其他API视图都应继承这些基础类。
"""

import logging
from typing import Any, Dict, Optional
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny

from ..logging.logger import get_logger, performance_monitor, log_exceptions
from ..exceptions import TuanziException

logger = get_logger(__name__)


class BaseAPIView(APIView):
    """
    基础API视图类

    提供统一的响应格式和错误处理机制。
    所有API视图都应继承此类。
    """

    def success_response(
        self,
        data: Any = None,
        message: str = "操作成功",
        status_code: int = status.HTTP_200_OK,
        extra: Dict[str, Any] = None
    ) -> Response:
        """
        成功响应格式

        Args:
            data: 响应数据
            message: 响应消息
            status_code: HTTP状态码
            extra: 额外的响应字段

        Returns:
            Response: 格式化的响应
        """
        response_data = {
            'success': True,
            'message': message,
            'data': data
        }

        if extra:
            response_data.update(extra)

        return Response(response_data, status=status_code)

    def error_response(
        self,
        message: str = "操作失败",
        error_code: str = None,
        details: Dict[str, Any] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST
    ) -> Response:
        """
        错误响应格式

        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
            status_code: HTTP状态码

        Returns:
            Response: 格式化的错误响应
        """
        response_data = {
            'success': False,
            'message': message,
            'error_code': error_code,
            'details': details
        }

        return Response(response_data, status=status_code)

    def handle_exception(self, exc: Exception) -> Response:
        """
        统一异常处理

        Args:
            exc: 异常实例

        Returns:
            Response: 错误响应
        """
        if isinstance(exc, TuanziException):
            return self.error_response(
                message=str(exc),
                error_code=exc.error_code,
                details=exc.details,
                status_code=exc.get_http_status()
            )

        # 记录未处理的异常
        logger.error(
            f"未处理的异常: {type(exc).__name__}: {str(exc)}",
            extra={'exception_type': type(exc).__name__, 'exception_message': str(exc)},
            exc_info=True
        )

        return self.error_response(
            message="服务器内部错误",
            error_code="INTERNAL_SERVER_ERROR",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

    def dispatch(self, request, *args, **kwargs):
        """
        重写dispatch方法以添加统一的异常处理

        Args:
            request: HTTP请求
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            Response: HTTP响应
        """
        try:
            return super().dispatch(request, *args, **kwargs)
        except Exception as exc:
            return self.handle_exception(exc)


class APIRootView(BaseAPIView):
    """
    API根视图

    提供API的基本信息和可用端点列表。
    """
    permission_classes = [AllowAny]

    @performance_monitor('api.root')
    def get(self, request, *args, **kwargs):
        """
        获取API根信息

        Returns:
            Response: API基本信息
        """
        api_info = {
            'name': 'Tuanzi API',
            'version': '1.0.0',
            'description': '团子团建活动平台API',
            'endpoints': {
                'auth': {
                    'register': '/api/register/',
                    'login': '/api/token/',
                    'health': '/api/health/'
                },
                'rooms': {
                    'create': '/api/rooms/create/',
                    'join': '/api/rooms/join/',
                    'detail': '/api/rooms/{room_code}/'
                },
                'reservations': {
                    'schedule': '/api/reservations/schedule/',
                    'detail': '/api/reservations/{reservation_id}/',
                    'calendar': '/api/reservations/calendar/'
                },
                'templates': {
                    'list': '/api/templates/'
                },
                'subscription': {
                    'manage': '/api/subscription/'
                }
            }
        }

        return self.success_response(
            data=api_info,
            message="欢迎使用团子API"
        )


class HealthCheckView(BaseAPIView):
    """
    健康检查视图

    用于验证用户认证状态和会话有效性。
    提供系统健康状态信息。
    """
    permission_classes = [IsAuthenticated]

    @performance_monitor('api.health_check')
    @log_exceptions('api')
    def get(self, request, *args, **kwargs):
        """
        健康检查端点

        验证用户认证状态并返回系统健康信息。

        Returns:
            Response: 健康检查结果
        """
        user = request.user
        
        health_data = {
            'status': 'healthy',
            'timestamp': logger.get_current_timestamp(),
            'user': {
                'id': user.id,
                'username': user.username,
                'subscription_level': user.subscription_level,
                'is_authenticated': True
            },
            'system': {
                'database': 'connected',
                'cache': 'available',
                'logging': 'active'
            }
        }

        logger.info(
            f"健康检查请求",
            extra={
                'user_id': user.id,
                'username': user.username,
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT')
            }
        )

        return self.success_response(
            data=health_data,
            message="系统运行正常"
        )

    @performance_monitor('api.health_check_post')
    def post(self, request, *args, **kwargs):
        """
        健康检查POST端点

        用于更详细的健康检查，包括用户会话验证。

        Returns:
            Response: 详细健康检查结果
        """
        user = request.user
        
        # 执行更详细的健康检查
        detailed_health = {
            'status': 'healthy',
            'timestamp': logger.get_current_timestamp(),
            'user_session': {
                'user_id': user.id,
                'username': user.username,
                'subscription_level': user.subscription_level,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'date_joined': user.date_joined.isoformat(),
                'is_active': user.is_active
            },
            'request_info': {
                'method': request.method,
                'path': request.path,
                'ip_address': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT', '')[:100]  # 限制长度
            },
            'system_checks': {
                'database_connection': True,
                'user_authentication': True,
                'permission_system': True
            }
        }

        return self.success_response(
            data=detailed_health,
            message="详细健康检查完成"
        )
