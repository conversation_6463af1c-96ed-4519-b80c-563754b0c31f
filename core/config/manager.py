"""
配置管理器

提供统一的配置管理接口，包括：
- 多源配置加载
- 配置验证和类型转换
- 配置缓存和热更新
- 环境变量支持

遵循配置分离和环境隔离原则。
"""

import os
import json
from typing import Any, Dict, Optional, Union, List
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ImproperlyConfigured

from ..logging.logger import get_logger
from ..exceptions.base_exceptions import ConfigurationException

logger = get_logger(__name__)


class ConfigManager:
    """
    配置管理器

    提供统一的配置管理功能，包括：
    - 多源配置加载（环境变量、文件、数据库）
    - 配置验证和类型转换
    - 配置缓存和热更新
    - 默认值和必需配置检查
    """

    def __init__(self):
        """初始化配置管理器"""
        self._config_cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self._environment = os.getenv('DJANGO_ENV', 'development')
        self._config_sources = []
        self._load_default_sources()

    def _load_default_sources(self):
        """加载默认配置源"""
        # 环境变量优先级最高
        self._config_sources = [
            self._load_from_environment,
            self._load_from_settings,
            self._load_from_file,
        ]

    def _load_from_environment(self, key: str) -> Optional[Any]:
        """从环境变量加载配置"""
        env_key = key.upper().replace('.', '_')
        value = os.getenv(env_key)
        if value is not None:
            logger.debug(f"从环境变量加载配置: {env_key} = {value}")
        return value

    def _load_from_settings(self, key: str) -> Optional[Any]:
        """从Django settings加载配置"""
        try:
            # 支持嵌套键，如 'database.host'
            keys = key.split('.')
            value = settings
            for k in keys:
                value = getattr(value, k.upper(), None)
                if value is None:
                    break
            
            if value is not None:
                logger.debug(f"从Django settings加载配置: {key} = {value}")
            return value
        except (AttributeError, ImproperlyConfigured):
            return None

    def _load_from_file(self, key: str) -> Optional[Any]:
        """从配置文件加载配置"""
        config_file = os.path.join(
            settings.BASE_DIR,
            'config',
            f'{self._environment}.json'
        )
        
        if not os.path.exists(config_file):
            return None

        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 支持嵌套键
            keys = key.split('.')
            value = config_data
            for k in keys:
                value = value.get(k)
                if value is None:
                    break
            
            if value is not None:
                logger.debug(f"从配置文件加载配置: {key} = {value}")
            return value
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
            logger.warning(f"从配置文件加载配置失败: {key}, 错误: {str(e)}")
            return None

    def _get_cache_key(self, key: str) -> str:
        """获取缓存键"""
        return f"config:{self._environment}:{key}"

    def _convert_type(self, value: Any, target_type: type) -> Any:
        """类型转换"""
        if value is None:
            return None

        if isinstance(value, target_type):
            return value

        try:
            if target_type == bool:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif target_type == int:
                return int(value)
            elif target_type == float:
                return float(value)
            elif target_type == list:
                if isinstance(value, str):
                    return json.loads(value) if value.startswith('[') else value.split(',')
                return list(value)
            elif target_type == dict:
                if isinstance(value, str):
                    return json.loads(value)
                return dict(value)
            else:
                return target_type(value)
        except (ValueError, TypeError, json.JSONDecodeError) as e:
            raise ConfigurationException(
                f"配置类型转换失败: {value} -> {target_type.__name__}",
                config_key=str(value),
                config_value=value,
                original_exception=e
            )

    def get(
        self,
        key: str,
        default: Any = None,
        required: bool = False,
        config_type: type = str,
        use_cache: bool = True
    ) -> Any:
        """
        获取配置值

        Args:
            key: 配置键，支持嵌套键（如 'database.host'）
            default: 默认值
            required: 是否为必需配置
            config_type: 配置类型
            use_cache: 是否使用缓存

        Returns:
            Any: 配置值

        Raises:
            ConfigurationException: 必需配置缺失或类型转换失败
        """
        cache_key = self._get_cache_key(key)

        # 尝试从缓存获取
        if use_cache:
            cached_value = cache.get(cache_key)
            if cached_value is not None:
                return self._convert_type(cached_value, config_type)

        # 从配置源加载
        value = None
        for source in self._config_sources:
            try:
                value = source(key)
                if value is not None:
                    break
            except Exception as e:
                logger.warning(f"配置源加载失败: {key}, 错误: {str(e)}")
                continue

        # 使用默认值
        if value is None:
            value = default

        # 检查必需配置
        if required and value is None:
            raise ConfigurationException(
                f"必需配置缺失: {key}",
                config_key=key
            )

        # 类型转换
        if value is not None:
            value = self._convert_type(value, config_type)

        # 缓存配置
        if use_cache and value is not None:
            cache.set(cache_key, value, self._cache_timeout)

        logger.debug(f"获取配置: {key} = {value}")
        return value

    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'host': self.get('database.host', 'localhost'),
            'port': self.get('database.port', 5432, config_type=int),
            'name': self.get('database.name', 'tuanzi'),
            'user': self.get('database.user', 'postgres'),
            'password': self.get('database.password', '', required=True),
            'options': self.get('database.options', {}, config_type=dict),
        }

    def get_redis_config(self) -> Dict[str, Any]:
        """获取Redis配置"""
        return {
            'host': self.get('redis.host', 'localhost'),
            'port': self.get('redis.port', 6379, config_type=int),
            'db': self.get('redis.db', 0, config_type=int),
            'password': self.get('redis.password'),
            'max_connections': self.get('redis.max_connections', 50, config_type=int),
        }

    def get_websocket_config(self) -> Dict[str, Any]:
        """获取WebSocket配置"""
        return {
            'host': self.get('websocket.host', '0.0.0.0'),
            'port': self.get('websocket.port', 8000, config_type=int),
            'path': self.get('websocket.path', '/ws/'),
            'heartbeat_interval': self.get('websocket.heartbeat_interval', 30, config_type=int),
            'max_connections': self.get('websocket.max_connections', 1000, config_type=int),
        }

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'level': self.get('logging.level', 'INFO'),
            'format': self.get('logging.format', 'json'),
            'file_path': self.get('logging.file_path', 'logs/tuanzi.log'),
            'max_file_size': self.get('logging.max_file_size', '10MB'),
            'backup_count': self.get('logging.backup_count', 5, config_type=int),
            'enable_database_logging': self.get('logging.enable_database', False, config_type=bool),
        }

    def get_subscription_config(self) -> Dict[str, Any]:
        """获取订阅配置"""
        return {
            'free_room_limit': self.get('subscription.free.room_limit', 5, config_type=int),
            'free_duration_hours': self.get('subscription.free.duration_hours', 2, config_type=int),
            'free_max_participants': self.get('subscription.free.max_participants', 10, config_type=int),
            'pro_room_limit': self.get('subscription.pro.room_limit', 20, config_type=int),
            'pro_duration_hours': self.get('subscription.pro.duration_hours', 24, config_type=int),
            'pro_max_participants': self.get('subscription.pro.max_participants', 500, config_type=int),
            'max_room_limit': self.get('subscription.max.room_limit', -1, config_type=int),
            'max_duration_hours': self.get('subscription.max.duration_hours', 72, config_type=int),
            'max_max_participants': self.get('subscription.max.max_participants', 2000, config_type=int),
        }

    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return {
            'jwt_secret_key': self.get('security.jwt_secret_key', required=True),
            'jwt_expiration_hours': self.get('security.jwt_expiration_hours', 24, config_type=int),
            'password_min_length': self.get('security.password_min_length', 8, config_type=int),
            'max_login_attempts': self.get('security.max_login_attempts', 5, config_type=int),
            'lockout_duration_minutes': self.get('security.lockout_duration_minutes', 30, config_type=int),
        }

    def reload_config(self, key: str = None):
        """重新加载配置"""
        if key:
            cache_key = self._get_cache_key(key)
            cache.delete(cache_key)
            logger.info(f"重新加载配置: {key}")
        else:
            # 清除所有配置缓存
            cache.delete_many([
                self._get_cache_key(k) for k in self._config_cache.keys()
            ])
            self._config_cache.clear()
            logger.info("重新加载所有配置")

    def validate_config(self) -> List[str]:
        """验证配置完整性"""
        errors = []
        
        # 验证必需配置
        required_configs = [
            ('database.password', str),
            ('security.jwt_secret_key', str),
        ]
        
        for key, config_type in required_configs:
            try:
                self.get(key, required=True, config_type=config_type)
            except ConfigurationException as e:
                errors.append(str(e))
        
        return errors


# 全局配置管理器实例
_config_manager = None


def get_config() -> ConfigManager:
    """
    获取全局配置管理器实例

    Returns:
        ConfigManager: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
