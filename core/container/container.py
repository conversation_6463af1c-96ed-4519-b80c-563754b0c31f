"""
依赖注入容器

提供轻量级的依赖注入功能，包括：
- 服务注册和解析
- 生命周期管理
- 循环依赖检测
- 自动装配

遵循控制反转原则，实现松耦合架构。
"""

import inspect
from typing import Any, Dict, Type, TypeVar, Callable, Optional, List
from threading import Lock

from ..logging.logger import get_logger
from ..exceptions.base_exceptions import ConfigurationException

logger = get_logger(__name__)

T = TypeVar('T')


class ServiceLifetime:
    """服务生命周期枚举"""
    SINGLETON = 'singleton'
    TRANSIENT = 'transient'
    SCOPED = 'scoped'


class ServiceDescriptor:
    """
    服务描述符

    描述服务的注册信息，包括：
    - 服务类型和实现
    - 生命周期管理
    - 工厂函数
    - 依赖关系
    """

    def __init__(
        self,
        service_type: Type[T],
        implementation: Type[T] = None,
        factory: Callable[[], T] = None,
        lifetime: str = ServiceLifetime.TRANSIENT,
        instance: T = None
    ):
        """
        初始化服务描述符

        Args:
            service_type: 服务类型（接口或抽象类）
            implementation: 服务实现类
            factory: 工厂函数
            lifetime: 生命周期
            instance: 单例实例
        """
        self.service_type = service_type
        self.implementation = implementation or service_type
        self.factory = factory
        self.lifetime = lifetime
        self.instance = instance
        self.dependencies = []

    def __repr__(self):
        return f"ServiceDescriptor({self.service_type.__name__}, {self.lifetime})"


class Container:
    """
    依赖注入容器

    提供服务注册、解析和生命周期管理功能。
    支持构造函数注入和循环依赖检测。
    """

    def __init__(self):
        """初始化容器"""
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._instances: Dict[Type, Any] = {}
        self._lock = Lock()
        self._resolving_stack: List[Type] = []

    def register_singleton(
        self,
        service_type: Type[T],
        implementation: Type[T] = None,
        factory: Callable[[], T] = None,
        instance: T = None
    ) -> 'Container':
        """
        注册单例服务

        Args:
            service_type: 服务类型
            implementation: 实现类型
            factory: 工厂函数
            instance: 现有实例

        Returns:
            Container: 容器实例（支持链式调用）
        """
        return self._register(
            service_type,
            implementation,
            factory,
            ServiceLifetime.SINGLETON,
            instance
        )

    def register_transient(
        self,
        service_type: Type[T],
        implementation: Type[T] = None,
        factory: Callable[[], T] = None
    ) -> 'Container':
        """
        注册瞬态服务

        Args:
            service_type: 服务类型
            implementation: 实现类型
            factory: 工厂函数

        Returns:
            Container: 容器实例（支持链式调用）
        """
        return self._register(
            service_type,
            implementation,
            factory,
            ServiceLifetime.TRANSIENT
        )

    def register_scoped(
        self,
        service_type: Type[T],
        implementation: Type[T] = None,
        factory: Callable[[], T] = None
    ) -> 'Container':
        """
        注册作用域服务

        Args:
            service_type: 服务类型
            implementation: 实现类型
            factory: 工厂函数

        Returns:
            Container: 容器实例（支持链式调用）
        """
        return self._register(
            service_type,
            implementation,
            factory,
            ServiceLifetime.SCOPED
        )

    def _register(
        self,
        service_type: Type[T],
        implementation: Type[T] = None,
        factory: Callable[[], T] = None,
        lifetime: str = ServiceLifetime.TRANSIENT,
        instance: T = None
    ) -> 'Container':
        """
        内部注册方法

        Args:
            service_type: 服务类型
            implementation: 实现类型
            factory: 工厂函数
            lifetime: 生命周期
            instance: 现有实例

        Returns:
            Container: 容器实例
        """
        with self._lock:
            descriptor = ServiceDescriptor(
                service_type=service_type,
                implementation=implementation,
                factory=factory,
                lifetime=lifetime,
                instance=instance
            )

            # 分析依赖关系
            if implementation and not factory:
                descriptor.dependencies = self._analyze_dependencies(implementation)

            self._services[service_type] = descriptor
            
            logger.debug(f"注册服务: {service_type.__name__} ({lifetime})")
            
            return self

    def _analyze_dependencies(self, implementation: Type) -> List[Type]:
        """
        分析构造函数依赖

        Args:
            implementation: 实现类型

        Returns:
            List[Type]: 依赖类型列表
        """
        try:
            signature = inspect.signature(implementation.__init__)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
                else:
                    logger.warning(f"服务 {implementation.__name__} 的参数 {param_name} 缺少类型注解")
            
            return dependencies
        except Exception as e:
            logger.warning(f"分析依赖失败: {implementation.__name__}, 错误: {str(e)}")
            return []

    def resolve(self, service_type: Type[T]) -> T:
        """
        解析服务实例

        Args:
            service_type: 服务类型

        Returns:
            T: 服务实例

        Raises:
            ConfigurationException: 服务未注册或循环依赖
        """
        with self._lock:
            return self._resolve_internal(service_type)

    def _resolve_internal(self, service_type: Type[T]) -> T:
        """
        内部解析方法

        Args:
            service_type: 服务类型

        Returns:
            T: 服务实例
        """
        # 检查循环依赖
        if service_type in self._resolving_stack:
            cycle = ' -> '.join([t.__name__ for t in self._resolving_stack])
            cycle += f' -> {service_type.__name__}'
            raise ConfigurationException(
                f"检测到循环依赖: {cycle}",
                config_key='dependency_injection'
            )

        # 检查服务是否已注册
        if service_type not in self._services:
            raise ConfigurationException(
                f"服务未注册: {service_type.__name__}",
                config_key='service_registration'
            )

        descriptor = self._services[service_type]

        # 单例模式：检查是否已有实例
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            if descriptor.instance is not None:
                return descriptor.instance
            
            if service_type in self._instances:
                return self._instances[service_type]

        # 添加到解析栈
        self._resolving_stack.append(service_type)

        try:
            # 创建实例
            if descriptor.factory:
                instance = descriptor.factory()
            else:
                # 解析依赖
                dependencies = []
                for dep_type in descriptor.dependencies:
                    dep_instance = self._resolve_internal(dep_type)
                    dependencies.append(dep_instance)
                
                # 创建实例
                instance = descriptor.implementation(*dependencies)

            # 单例模式：缓存实例
            if descriptor.lifetime == ServiceLifetime.SINGLETON:
                self._instances[service_type] = instance
                descriptor.instance = instance

            logger.debug(f"解析服务: {service_type.__name__}")
            return instance

        finally:
            # 从解析栈移除
            self._resolving_stack.pop()

    def is_registered(self, service_type: Type) -> bool:
        """
        检查服务是否已注册

        Args:
            service_type: 服务类型

        Returns:
            bool: 是否已注册
        """
        return service_type in self._services

    def get_registered_services(self) -> List[Type]:
        """
        获取已注册的服务类型列表

        Returns:
            List[Type]: 服务类型列表
        """
        return list(self._services.keys())

    def clear(self):
        """清空容器"""
        with self._lock:
            self._services.clear()
            self._instances.clear()
            self._resolving_stack.clear()
            logger.info("清空依赖注入容器")

    def create_scope(self) -> 'ScopedContainer':
        """
        创建作用域容器

        Returns:
            ScopedContainer: 作用域容器
        """
        return ScopedContainer(self)


class ScopedContainer:
    """
    作用域容器

    为作用域服务提供独立的实例管理。
    """

    def __init__(self, parent: Container):
        """
        初始化作用域容器

        Args:
            parent: 父容器
        """
        self._parent = parent
        self._scoped_instances: Dict[Type, Any] = {}

    def resolve(self, service_type: Type[T]) -> T:
        """
        解析服务实例

        Args:
            service_type: 服务类型

        Returns:
            T: 服务实例
        """
        if service_type not in self._parent._services:
            return self._parent.resolve(service_type)

        descriptor = self._parent._services[service_type]

        if descriptor.lifetime == ServiceLifetime.SCOPED:
            if service_type in self._scoped_instances:
                return self._scoped_instances[service_type]
            
            instance = self._parent._resolve_internal(service_type)
            self._scoped_instances[service_type] = instance
            return instance
        
        return self._parent.resolve(service_type)

    def dispose(self):
        """释放作用域资源"""
        self._scoped_instances.clear()


# 全局容器实例
_container = None


def get_container() -> Container:
    """
    获取全局容器实例

    Returns:
        Container: 容器实例
    """
    global _container
    if _container is None:
        _container = Container()
    return _container
