"""
统一异常处理模块

提供团子APP的统一异常处理系统，包括：
- 基础异常类定义
- 业务异常类型
- 异常处理器
- 异常日志记录

遵循异常分层原则，提供清晰的错误信息。
"""

from .base_exceptions import (
    TuanziException,
    ValidationException,
    PermissionException,
    BusinessLogicException,
    ResourceNotFoundException,
    ConfigurationException,
    ExternalServiceException,
)

from .user_exceptions import (
    UserNotFoundException,
    InvalidCredentialsException,
    SubscriptionLimitExceededException,
    UserAlreadyExistsException,
    UserInactiveException,
    UserBannedException,
    InvalidSubscriptionLevelException,
    CheckInAlreadyExistsException,
)

from .room_exceptions import (
    RoomNotFoundException,
    RoomFullException,
    RoomStateException,
    RoomPermissionException,
    RoomExpiredException,
    ParticipantNotFoundException,
    InvalidRoomCodeException,
    InvalidRoomConfigurationException,
    RoomStepException,
)

__all__ = [
    # 基础异常
    'TuanziException',
    'ValidationException',
    'PermissionException',
    'BusinessLogicException',
    'ResourceNotFoundException',
    'ConfigurationException',
    'ExternalServiceException',

    # 用户异常
    'UserNotFoundException',
    'InvalidCredentialsException',
    'SubscriptionLimitExceededException',
    'UserAlreadyExistsException',
    'UserInactiveException',
    'UserBannedException',
    'InvalidSubscriptionLevelException',
    'CheckInAlreadyExistsException',

    # 房间异常
    'RoomNotFoundException',
    'RoomFullException',
    'RoomStateException',
    'RoomPermissionException',
    'RoomExpiredException',
    'ParticipantNotFoundException',
    'InvalidRoomCodeException',
    'InvalidRoomConfigurationException',
    'RoomStepException',
]
