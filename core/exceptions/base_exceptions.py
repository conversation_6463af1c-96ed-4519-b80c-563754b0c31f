"""
基础异常类定义

定义团子APP的基础异常类型，包括：
- 基础异常类
- 验证异常
- 权限异常
- 业务异常

所有自定义异常都应继承自这些基础异常类。
"""

from typing import Optional, Dict, Any


class TuanziException(Exception):
    """
    团子APP基础异常类

    所有自定义异常的基类，提供统一的异常处理接口
    """

    def __init__(
        self,
        message: str,
        error_code: str = None,
        details: Dict[str, Any] = None,
        original_exception: Exception = None
    ):
        """
        初始化异常

        Args:
            message: 错误消息
            error_code: 错误码
            details: 错误详情
            original_exception: 原始异常
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.original_exception = original_exception

    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式

        Returns:
            Dict[str, Any]: 异常信息字典
        """
        return {
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details,
            'exception_type': self.__class__.__name__,
        }

    def __str__(self) -> str:
        """字符串表示"""
        return f"[{self.error_code}] {self.message}"

    def __repr__(self) -> str:
        """调试表示"""
        return f"{self.__class__.__name__}(message='{self.message}', error_code='{self.error_code}')"


class ValidationException(TuanziException):
    """
    数据验证异常

    用于数据验证失败的场景
    """

    def __init__(
        self,
        message: str,
        field: str = None,
        value: Any = None,
        **kwargs
    ):
        """
        初始化验证异常

        Args:
            message: 错误消息
            field: 验证失败的字段
            value: 验证失败的值
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if field:
            details['field'] = field
        if value is not None:
            details['value'] = str(value)

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'VALIDATION_ERROR'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.field = field
        self.value = value


class PermissionException(TuanziException):
    """
    权限异常

    用于权限验证失败的场景
    """

    def __init__(
        self,
        message: str,
        required_permission: str = None,
        user_permissions: list = None,
        **kwargs
    ):
        """
        初始化权限异常

        Args:
            message: 错误消息
            required_permission: 所需权限
            user_permissions: 用户权限列表
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if required_permission:
            details['required_permission'] = required_permission
        if user_permissions:
            details['user_permissions'] = user_permissions

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'PERMISSION_DENIED'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.required_permission = required_permission
        self.user_permissions = user_permissions


class BusinessLogicException(TuanziException):
    """
    业务逻辑异常

    用于业务规则违反的场景
    """

    def __init__(
        self,
        message: str,
        business_rule: str = None,
        **kwargs
    ):
        """
        初始化业务逻辑异常

        Args:
            message: 错误消息
            business_rule: 违反的业务规则
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if business_rule:
            details['business_rule'] = business_rule

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'BUSINESS_LOGIC_ERROR'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.business_rule = business_rule


class ResourceNotFoundException(TuanziException):
    """
    资源未找到异常

    用于资源不存在的场景
    """

    def __init__(
        self,
        message: str,
        resource_type: str = None,
        resource_id: Any = None,
        **kwargs
    ):
        """
        初始化资源未找到异常

        Args:
            message: 错误消息
            resource_type: 资源类型
            resource_id: 资源ID
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if resource_type:
            details['resource_type'] = resource_type
        if resource_id is not None:
            details['resource_id'] = str(resource_id)

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'RESOURCE_NOT_FOUND'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.resource_type = resource_type
        self.resource_id = resource_id


class ConfigurationException(TuanziException):
    """
    配置异常

    用于配置错误的场景
    """

    def __init__(
        self,
        message: str,
        config_key: str = None,
        config_value: Any = None,
        **kwargs
    ):
        """
        初始化配置异常

        Args:
            message: 错误消息
            config_key: 配置键
            config_value: 配置值
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if config_key:
            details['config_key'] = config_key
        if config_value is not None:
            details['config_value'] = str(config_value)

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'CONFIGURATION_ERROR'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.config_key = config_key
        self.config_value = config_value


class ExternalServiceException(TuanziException):
    """
    外部服务异常

    用于外部服务调用失败的场景
    """

    def __init__(
        self,
        message: str,
        service_name: str = None,
        status_code: int = None,
        **kwargs
    ):
        """
        初始化外部服务异常

        Args:
            message: 错误消息
            service_name: 服务名称
            status_code: HTTP状态码
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if service_name:
            details['service_name'] = service_name
        if status_code:
            details['status_code'] = status_code

        super().__init__(
            message=message,
            error_code=kwargs.get('error_code', 'EXTERNAL_SERVICE_ERROR'),
            details=details,
            original_exception=kwargs.get('original_exception')
        )
        self.service_name = service_name
        self.status_code = status_code
