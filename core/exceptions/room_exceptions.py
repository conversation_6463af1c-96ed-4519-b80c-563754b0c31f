"""
房间相关异常定义

定义房间模块的专用异常类型，包括：
- 房间未找到异常
- 房间状态异常
- 房间权限异常
- 房间容量异常

所有房间相关的异常都应在此模块定义。
"""

from typing import Any, Dict, List
from .base_exceptions import (
    TuanziException,
    ValidationException,
    PermissionException,
    ResourceNotFoundException,
    BusinessLogicException
)


class RoomNotFoundException(ResourceNotFoundException):
    """
    房间未找到异常

    当房间不存在时抛出此异常
    """

    def __init__(
        self,
        message: str = "房间不存在",
        room_id: Any = None,
        room_code: str = None,
        **kwargs
    ):
        """
        初始化房间未找到异常

        Args:
            message: 错误消息
            room_id: 房间ID
            room_code: 房间码
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if room_id is not None:
            details['room_id'] = str(room_id)
        if room_code:
            details['room_code'] = room_code

        super().__init__(
            message=message,
            resource_type='Room',
            resource_id=room_id or room_code,
            error_code='ROOM_NOT_FOUND',
            details=details,
            **kwargs
        )


class RoomFullException(BusinessLogicException):
    """
    房间已满异常

    当房间参与者数量达到上限时抛出此异常
    """

    def __init__(
        self,
        message: str = "房间已满",
        room_code: str = None,
        current_count: int = None,
        max_count: int = None,
        **kwargs
    ):
        """
        初始化房间已满异常

        Args:
            message: 错误消息
            room_code: 房间码
            current_count: 当前人数
            max_count: 最大人数
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if room_code:
            details['room_code'] = room_code
        if current_count is not None:
            details['current_count'] = current_count
        if max_count is not None:
            details['max_count'] = max_count

        super().__init__(
            message=message,
            business_rule='room_capacity_limit',
            error_code='ROOM_FULL',
            details=details,
            **kwargs
        )
        self.current_count = current_count
        self.max_count = max_count


class RoomStateException(BusinessLogicException):
    """
    房间状态异常

    当房间状态不允许执行某操作时抛出此异常
    """

    def __init__(
        self,
        message: str = "房间状态不允许此操作",
        current_state: str = None,
        required_state: str = None,
        allowed_states: List[str] = None,
        target_state: str = None,
        **kwargs
    ):
        """
        初始化房间状态异常

        Args:
            message: 错误消息
            current_state: 当前状态
            required_state: 所需状态
            allowed_states: 允许的状态列表
            target_state: 目标状态
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if current_state:
            details['current_state'] = current_state
        if required_state:
            details['required_state'] = required_state
        if allowed_states:
            details['allowed_states'] = allowed_states
        if target_state:
            details['target_state'] = target_state

        super().__init__(
            message=message,
            business_rule='room_state_transition',
            error_code='INVALID_ROOM_STATE',
            details=details,
            **kwargs
        )
        self.current_state = current_state
        self.required_state = required_state
        self.allowed_states = allowed_states
        self.target_state = target_state


class RoomPermissionException(PermissionException):
    """
    房间权限异常

    当用户没有执行房间操作的权限时抛出此异常
    """

    def __init__(
        self,
        message: str = "没有房间操作权限",
        required_role: str = None,
        user_role: str = None,
        room_code: str = None,
        operation: str = None,
        **kwargs
    ):
        """
        初始化房间权限异常

        Args:
            message: 错误消息
            required_role: 所需角色
            user_role: 用户角色
            room_code: 房间码
            operation: 操作类型
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if required_role:
            details['required_role'] = required_role
        if user_role:
            details['user_role'] = user_role
        if room_code:
            details['room_code'] = room_code
        if operation:
            details['operation'] = operation

        super().__init__(
            message=message,
            required_permission=required_role,
            error_code='ROOM_PERMISSION_DENIED',
            details=details,
            **kwargs
        )
        self.required_role = required_role
        self.user_role = user_role
        self.operation = operation


class RoomExpiredException(BusinessLogicException):
    """
    房间过期异常

    当房间已过期时抛出此异常
    """

    def __init__(
        self,
        message: str = "房间已过期",
        room_code: str = None,
        expired_at: str = None,
        **kwargs
    ):
        """
        初始化房间过期异常

        Args:
            message: 错误消息
            room_code: 房间码
            expired_at: 过期时间
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if room_code:
            details['room_code'] = room_code
        if expired_at:
            details['expired_at'] = expired_at

        super().__init__(
            message=message,
            business_rule='room_expiration',
            error_code='ROOM_EXPIRED',
            details=details,
            **kwargs
        )


class ParticipantNotFoundException(ResourceNotFoundException):
    """
    参与者未找到异常

    当房间中的参与者不存在时抛出此异常
    """

    def __init__(
        self,
        message: str = "参与者不存在",
        user_id: Any = None,
        room_code: str = None,
        **kwargs
    ):
        """
        初始化参与者未找到异常

        Args:
            message: 错误消息
            user_id: 用户ID
            room_code: 房间码
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if user_id is not None:
            details['user_id'] = str(user_id)
        if room_code:
            details['room_code'] = room_code

        super().__init__(
            message=message,
            resource_type='RoomParticipant',
            resource_id=user_id,
            error_code='PARTICIPANT_NOT_FOUND',
            details=details,
            **kwargs
        )


class InvalidRoomCodeException(ValidationException):
    """
    无效房间码异常

    当房间码格式无效时抛出此异常
    """

    def __init__(
        self,
        message: str = "无效的房间码",
        room_code: str = None,
        **kwargs
    ):
        """
        初始化无效房间码异常

        Args:
            message: 错误消息
            room_code: 房间码
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if room_code:
            details['room_code'] = room_code

        super().__init__(
            message=message,
            field='room_code',
            value=room_code,
            error_code='INVALID_ROOM_CODE',
            details=details,
            **kwargs
        )


class InvalidRoomConfigurationException(ValidationException):
    """
    无效房间配置异常

    当房间配置参数无效时抛出此异常
    """

    def __init__(
        self,
        message: str = "无效的房间配置",
        config_field: str = None,
        config_value: Any = None,
        **kwargs
    ):
        """
        初始化无效房间配置异常

        Args:
            message: 错误消息
            config_field: 配置字段
            config_value: 配置值
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if config_field:
            details['config_field'] = config_field
        if config_value is not None:
            details['config_value'] = str(config_value)

        super().__init__(
            message=message,
            field=config_field,
            value=config_value,
            error_code='INVALID_ROOM_CONFIGURATION',
            details=details,
            **kwargs
        )


class RoomStepException(BusinessLogicException):
    """
    房间环节异常

    当房间环节操作失败时抛出此异常
    """

    def __init__(
        self,
        message: str = "房间环节操作失败",
        step_name: str = None,
        step_order: int = None,
        room_code: str = None,
        **kwargs
    ):
        """
        初始化房间环节异常

        Args:
            message: 错误消息
            step_name: 环节名称
            step_order: 环节顺序
            room_code: 房间码
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if step_name:
            details['step_name'] = step_name
        if step_order is not None:
            details['step_order'] = step_order
        if room_code:
            details['room_code'] = room_code

        super().__init__(
            message=message,
            business_rule='room_step_management',
            error_code='ROOM_STEP_ERROR',
            details=details,
            **kwargs
        )
        self.step_name = step_name
        self.step_order = step_order
