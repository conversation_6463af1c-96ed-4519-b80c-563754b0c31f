"""
用户相关异常定义

定义用户模块的专用异常类型，包括：
- 用户未找到异常
- 认证失败异常
- 订阅限制异常
- 用户权限异常

所有用户相关的异常都应在此模块定义。
"""

from typing import Any, Dict
from .base_exceptions import (
    TuanziException,
    ValidationException,
    PermissionException,
    ResourceNotFoundException
)


class UserNotFoundException(ResourceNotFoundException):
    """
    用户未找到异常

    当用户不存在时抛出此异常
    """

    def __init__(
        self,
        message: str = "用户不存在",
        user_id: Any = None,
        username: str = None,
        **kwargs
    ):
        """
        初始化用户未找到异常

        Args:
            message: 错误消息
            user_id: 用户ID
            username: 用户名
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if user_id is not None:
            details['user_id'] = str(user_id)
        if username:
            details['username'] = username

        super().__init__(
            message=message,
            resource_type='User',
            resource_id=user_id or username,
            error_code='USER_NOT_FOUND',
            details=details,
            **kwargs
        )


class InvalidCredentialsException(ValidationException):
    """
    无效凭据异常

    当用户认证失败时抛出此异常
    """

    def __init__(
        self,
        message: str = "用户名或密码错误",
        username: str = None,
        **kwargs
    ):
        """
        初始化无效凭据异常

        Args:
            message: 错误消息
            username: 用户名
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if username:
            details['username'] = username

        super().__init__(
            message=message,
            field='credentials',
            error_code='INVALID_CREDENTIALS',
            details=details,
            **kwargs
        )


class SubscriptionLimitExceededException(PermissionException):
    """
    订阅限制超出异常

    当用户超出订阅等级限制时抛出此异常
    """

    def __init__(
        self,
        message: str = "超出订阅等级限制",
        current_level: str = None,
        required_level: str = None,
        limit_type: str = None,
        current_usage: int = None,
        limit_value: int = None,
        **kwargs
    ):
        """
        初始化订阅限制超出异常

        Args:
            message: 错误消息
            current_level: 当前订阅等级
            required_level: 所需订阅等级
            limit_type: 限制类型
            current_usage: 当前使用量
            limit_value: 限制值
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if current_level:
            details['current_level'] = current_level
        if required_level:
            details['required_level'] = required_level
        if limit_type:
            details['limit_type'] = limit_type
        if current_usage is not None:
            details['current_usage'] = current_usage
        if limit_value is not None:
            details['limit_value'] = limit_value

        super().__init__(
            message=message,
            required_permission=required_level,
            error_code='SUBSCRIPTION_LIMIT_EXCEEDED',
            details=details,
            **kwargs
        )
        self.current_level = current_level
        self.required_level = required_level
        self.limit_type = limit_type
        self.current_usage = current_usage
        self.limit_value = limit_value


class UserAlreadyExistsException(ValidationException):
    """
    用户已存在异常

    当尝试创建已存在的用户时抛出此异常
    """

    def __init__(
        self,
        message: str = "用户已存在",
        username: str = None,
        email: str = None,
        **kwargs
    ):
        """
        初始化用户已存在异常

        Args:
            message: 错误消息
            username: 用户名
            email: 邮箱
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if username:
            details['username'] = username
        if email:
            details['email'] = email

        super().__init__(
            message=message,
            field='user_identity',
            error_code='USER_ALREADY_EXISTS',
            details=details,
            **kwargs
        )


class UserInactiveException(PermissionException):
    """
    用户未激活异常

    当用户账户未激活时抛出此异常
    """

    def __init__(
        self,
        message: str = "用户账户未激活",
        user_id: Any = None,
        **kwargs
    ):
        """
        初始化用户未激活异常

        Args:
            message: 错误消息
            user_id: 用户ID
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if user_id is not None:
            details['user_id'] = str(user_id)

        super().__init__(
            message=message,
            required_permission='active_account',
            error_code='USER_INACTIVE',
            details=details,
            **kwargs
        )


class UserBannedException(PermissionException):
    """
    用户被封禁异常

    当用户账户被封禁时抛出此异常
    """

    def __init__(
        self,
        message: str = "用户账户已被封禁",
        user_id: Any = None,
        ban_reason: str = None,
        ban_until: str = None,
        **kwargs
    ):
        """
        初始化用户被封禁异常

        Args:
            message: 错误消息
            user_id: 用户ID
            ban_reason: 封禁原因
            ban_until: 封禁到期时间
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if user_id is not None:
            details['user_id'] = str(user_id)
        if ban_reason:
            details['ban_reason'] = ban_reason
        if ban_until:
            details['ban_until'] = ban_until

        super().__init__(
            message=message,
            required_permission='unbanned_account',
            error_code='USER_BANNED',
            details=details,
            **kwargs
        )
        self.ban_reason = ban_reason
        self.ban_until = ban_until


class InvalidSubscriptionLevelException(ValidationException):
    """
    无效订阅等级异常

    当订阅等级无效时抛出此异常
    """

    def __init__(
        self,
        message: str = "无效的订阅等级",
        subscription_level: str = None,
        valid_levels: list = None,
        **kwargs
    ):
        """
        初始化无效订阅等级异常

        Args:
            message: 错误消息
            subscription_level: 订阅等级
            valid_levels: 有效等级列表
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if subscription_level:
            details['subscription_level'] = subscription_level
        if valid_levels:
            details['valid_levels'] = valid_levels

        super().__init__(
            message=message,
            field='subscription_level',
            value=subscription_level,
            error_code='INVALID_SUBSCRIPTION_LEVEL',
            details=details,
            **kwargs
        )


class CheckInAlreadyExistsException(ValidationException):
    """
    签到已存在异常

    当用户今日已签到时抛出此异常
    """

    def __init__(
        self,
        message: str = "今日已签到",
        user_id: Any = None,
        check_in_date: str = None,
        **kwargs
    ):
        """
        初始化签到已存在异常

        Args:
            message: 错误消息
            user_id: 用户ID
            check_in_date: 签到日期
            **kwargs: 其他参数
        """
        details = kwargs.get('details', {})
        if user_id is not None:
            details['user_id'] = str(user_id)
        if check_in_date:
            details['check_in_date'] = check_in_date

        super().__init__(
            message=message,
            field='check_in',
            error_code='CHECK_IN_ALREADY_EXISTS',
            details=details,
            **kwargs
        )
