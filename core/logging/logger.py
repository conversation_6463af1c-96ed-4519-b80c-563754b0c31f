"""
结构化日志记录器

提供统一的日志记录接口，包括：
- 结构化日志记录
- 上下文信息管理
- 日志级别控制
- 性能监控

遵循结构化日志原则，便于日志分析和监控。
"""

import logging
import json
import time
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from django.conf import settings
from django.utils import timezone


class StructuredLogger:
    """
    结构化日志记录器

    提供结构化的日志记录功能，包括：
    - 统一的日志格式
    - 上下文信息管理
    - 性能监控
    - 错误追踪
    """

    def __init__(self, name: str):
        """
        初始化结构化日志记录器

        Args:
            name: 日志记录器名称
        """
        self.logger = logging.getLogger(name)
        self.name = name
        self.context = {}

    def set_context(self, **kwargs):
        """
        设置日志上下文

        Args:
            **kwargs: 上下文键值对
        """
        self.context.update(kwargs)

    def clear_context(self):
        """清除日志上下文"""
        self.context.clear()

    def _format_message(
        self,
        level: str,
        message: str,
        extra: Dict[str, Any] = None,
        exception: Exception = None
    ) -> Dict[str, Any]:
        """
        格式化日志消息

        Args:
            level: 日志级别
            message: 日志消息
            extra: 额外信息
            exception: 异常对象

        Returns:
            Dict[str, Any]: 格式化的日志数据
        """
        log_data = {
            'timestamp': timezone.now().isoformat(),
            'level': level,
            'logger': self.name,
            'message': message,
            'context': self.context.copy(),
        }

        if extra:
            log_data['extra'] = extra

        if exception:
            log_data['exception'] = {
                'type': exception.__class__.__name__,
                'message': str(exception),
                'traceback': traceback.format_exc(),
            }

        return log_data

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        log_data = self._format_message('DEBUG', message, kwargs.get('extra'), kwargs.get('exception'))
        self.logger.debug(json.dumps(log_data, ensure_ascii=False))

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        log_data = self._format_message('INFO', message, kwargs.get('extra'), kwargs.get('exception'))
        self.logger.info(json.dumps(log_data, ensure_ascii=False))

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        log_data = self._format_message('WARNING', message, kwargs.get('extra'), kwargs.get('exception'))
        self.logger.warning(json.dumps(log_data, ensure_ascii=False))

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        log_data = self._format_message('ERROR', message, kwargs.get('extra'), kwargs.get('exception'))
        self.logger.error(json.dumps(log_data, ensure_ascii=False))

    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        log_data = self._format_message('CRITICAL', message, kwargs.get('extra'), kwargs.get('exception'))
        self.logger.critical(json.dumps(log_data, ensure_ascii=False))

    def log_user_action(
        self,
        user_id: int,
        action: str,
        resource_type: str = None,
        resource_id: str = None,
        result: str = 'success',
        **kwargs
    ):
        """
        记录用户行为日志

        Args:
            user_id: 用户ID
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            result: 操作结果
            **kwargs: 其他信息
        """
        extra = {
            'user_id': user_id,
            'action': action,
            'result': result,
            **kwargs
        }

        if resource_type:
            extra['resource_type'] = resource_type
        if resource_id:
            extra['resource_id'] = resource_id

        self.info(f"用户操作: {action}", extra=extra)

    def log_room_event(
        self,
        room_id: int,
        event_type: str,
        user_id: int = None,
        event_data: Dict[str, Any] = None,
        **kwargs
    ):
        """
        记录房间事件日志

        Args:
            room_id: 房间ID
            event_type: 事件类型
            user_id: 用户ID
            event_data: 事件数据
            **kwargs: 其他信息
        """
        extra = {
            'room_id': room_id,
            'event_type': event_type,
            **kwargs
        }

        if user_id:
            extra['user_id'] = user_id
        if event_data:
            extra['event_data'] = event_data

        self.info(f"房间事件: {event_type}", extra=extra)

    def log_api_request(
        self,
        method: str,
        path: str,
        user_id: int = None,
        status_code: int = None,
        duration: float = None,
        **kwargs
    ):
        """
        记录API请求日志

        Args:
            method: HTTP方法
            path: 请求路径
            user_id: 用户ID
            status_code: 响应状态码
            duration: 请求耗时
            **kwargs: 其他信息
        """
        extra = {
            'method': method,
            'path': path,
            **kwargs
        }

        if user_id:
            extra['user_id'] = user_id
        if status_code:
            extra['status_code'] = status_code
        if duration:
            extra['duration'] = duration

        level = 'error' if status_code and status_code >= 400 else 'info'
        message = f"API请求: {method} {path}"

        if level == 'error':
            self.error(message, extra=extra)
        else:
            self.info(message, extra=extra)

    def log_performance(
        self,
        operation: str,
        duration: float,
        success: bool = True,
        **kwargs
    ):
        """
        记录性能日志

        Args:
            operation: 操作名称
            duration: 耗时（秒）
            success: 是否成功
            **kwargs: 其他信息
        """
        extra = {
            'operation': operation,
            'duration': duration,
            'success': success,
            **kwargs
        }

        message = f"性能监控: {operation} ({duration:.3f}s)"
        
        if duration > 1.0:  # 超过1秒的操作记录为警告
            self.warning(message, extra=extra)
        else:
            self.debug(message, extra=extra)


def get_logger(name: str) -> StructuredLogger:
    """
    获取结构化日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        StructuredLogger: 结构化日志记录器实例
    """
    return StructuredLogger(name)


def performance_monitor(operation_name: str = None):
    """
    性能监控装饰器

    Args:
        operation_name: 操作名称

    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(func.__module__)
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.log_performance(op_name, duration, success=True)
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.log_performance(op_name, duration, success=False, exception=str(e))
                raise
        return wrapper
    return decorator


def log_exceptions(logger_name: str = None):
    """
    异常日志装饰器

    Args:
        logger_name: 日志记录器名称

    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(
                    f"函数 {func.__name__} 执行异常",
                    extra={
                        'function': func.__name__,
                        'module': func.__module__,
                        'args': str(args),
                        'kwargs': str(kwargs),
                    },
                    exception=e
                )
                raise
        return wrapper
    return decorator
