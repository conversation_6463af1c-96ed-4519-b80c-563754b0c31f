"""
房间管理模块

本模块负责房间相关的所有功能，包括：
- 房间模型定义
- 房间状态机管理
- 房间业务逻辑服务
- 房间数据访问层
- 房间参与者管理

遵循单一职责原则，只关心房间相关的功能。
"""

from .models import Room, RoomParticipant, RoomEventStep, RoomState, UserState
from .services import RoomService
from .repositories import RoomRepository, RoomParticipantRepository
from .state_machine import RoomStateMachine, get_room_state_machine

__all__ = [
    'Room',
    'RoomParticipant',
    'RoomEventStep',
    'RoomState',
    'UserState',
    'RoomService',
    'RoomRepository',
    'RoomParticipantRepository',
    'RoomStateMachine',
    'get_room_state_machine',
]
