"""
房间模型模块

定义房间相关的数据模型，包括：
- 房间模型
- 房间参与者模型
- 房间环节模型
- 房间状态枚举

技术特性：
- 基于Django ORM的数据建模
- 支持房间状态机管理
- 实现房间活动流程的实例解耦
- 提供完整的房间权限体系
"""

from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
import json


class RoomState(models.TextChoices):
    """
    房间状态枚举 - 严格按照状态机设计

    完整的房间生命周期状态：

    ### 房间状态机                                                                     
    |           |是否在房间开始时间之后|是否有人加入|是否满足下一环节开始条件|备注|
    |---        |:---:|:---:|:---:|:---:|
    |SCHEDULED  |F|/|/|为预约房间预留的状态|
    |OPEN       |T|F|/|/|
    |WAITING    |T|T|F|/|
    |READY      |T|T|T|/|
    |IN_PROGRESS|T|T|/|正在执行环节逻辑|
    |ENDED      |F|/|/|/|
    |CLOSED     |F|/|/|/

    ### 切换状态机的规则：

    ##### SCHEDULED：
        - SCHEDULED -> OPEN：到达房间的开始时间

    ##### OPEN：
        - OPEN -> WAITING：有人加入房间
        - OPEN -> ENDED：超过房间结束时间

    ##### WAITING：
        - WAITING -> READY：满足下一环节开始条件
        - WAITING -> OPEN：最后一个人离开房间
        - WAITING -> ENDED：超过房间结束时间

    ##### READY：
        - READY -> IN_PROGRESS：房主开始下一次序的环节逻辑
        - READY -> WAITING：下一环节开始条件被破坏
        - READY -> ENDED：超过房间结束时间

    ##### IN_PROGRESS：
        - IN_PROGRESS -> READY：环节结束
        - IN_PROGRESS -> ENDED：超过房间结束时间

    ##### ENDED：
        - ENDED -> CLOSED：超过房间结束时间15分钟

    ##### CLOSED：

    """
    SCHEDULED = 'SCHEDULED', '已预约'           # 房间已创建，但未到预定开始时间
    OPEN = 'OPEN', '已开启'                     # 已到达预定时间，房间为空，等待玩家加入
    WAITING = 'WAITING', '等待中'               # 有房主加入，正在检测房间条件（人数等）
    READY = 'READY', '准备就绪'                 # 房间条件满足，房主可以开始活动
    IN_PROGRESS = 'IN_PROGRESS', '活动中'       # 房主已开始活动，正在进行游戏
    ENDED = 'ENDED', '已结束'                   # 活动正常结束，可能处于计分或总结页面
    CLOSED = 'CLOSED', '已关闭'                 # 房间生命周期结束，被系统回收


class UserState(models.TextChoices):
    """
    用户在房间中的状态枚举
    """
    JOINED = 'JOINED', '已加入'
    READY = 'READY', '准备就绪'
    PLAYING = 'PLAYING', '游戏中'
    SPECTATING = 'SPECTATING', '观战中'


class Room(models.Model):
    """
    房间模型

    房间是团子APP的核心实体，代表一个团建活动会话。
    每个房间都有独立的生命周期和状态管理。

    主要功能：
    - 房间状态机管理
    - 参与者权限控制
    - 活动流程管理
    - 订阅等级限制
    """

    # 使用新的枚举定义
    STATUS_SCHEDULED = RoomState.SCHEDULED
    STATUS_OPEN = RoomState.OPEN
    STATUS_WAITING = RoomState.WAITING
    STATUS_READY = RoomState.READY
    STATUS_IN_PROGRESS = RoomState.IN_PROGRESS
    STATUS_ENDED = RoomState.ENDED
    STATUS_CLOSED = RoomState.CLOSED
    STATUS_CHOICES = RoomState.choices

    room_code = models.CharField(max_length=10, unique=True, blank=True)
    name = models.CharField(max_length=100, blank=True, help_text='房间名称，用于预约房间的显示')
    host = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='hosted_rooms',
        null=True,
        blank=True
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_OPEN)
    created_at = models.DateTimeField(auto_now_add=True)

    # 房间生命周期时间管理
    expires_at = models.DateTimeField(null=True, blank=True, help_text='房间过期时间，超过此时间房间将被关闭')
    closed_at = models.DateTimeField(null=True, blank=True, help_text='房间关闭时间，用于计算销毁时间')
    review_started_at = models.DateTimeField(null=True, blank=True, help_text='复盘阶段开始时间')
    last_activity_at = models.DateTimeField(auto_now_add=True, help_text='最后活动时间，用于检测空房间')

    # 预约系统相关字段
    scheduled_start_time = models.DateTimeField(null=True, blank=True, help_text='预约的开始时间，仅对SCHEDULED状态的房间有效')

    # 模板关联
    event_template = models.ForeignKey(
        'events.EventTemplate',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    system_template = models.ForeignKey(
        'core.SystemTemplate',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    # 当前环节跟踪
    current_step_order = models.PositiveIntegerField(default=0)
    current_event_step = models.ForeignKey(
        'RoomEventStep',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='current_in_rooms',
        help_text='当前正在执行的房间环节'
    )

    # 订阅等级限制
    max_participants = models.PositiveIntegerField(default=10, help_text="Maximum number of participants allowed")
    duration_hours = models.PositiveIntegerField(default=2, help_text="Room duration in hours")

    class Meta:
        db_table = 'core_room'
        verbose_name = '房间'
        verbose_name_plural = '房间'
        ordering = ['-created_at']

    def __str__(self):
        return f"房间 {self.room_code} - {self.name or '未命名'}"

    def set_limits_by_subscription(self, user):
        """
        根据用户订阅等级设置房间的参与人数和持续时间限制

        此方法在房间创建时调用，确保房间配置符合用户的订阅权限。
        不同订阅等级的限制策略：

        Free版限制：
        - 最大参与人数：10人（适合小型团队）
        - 房间持续时间：2小时（短期活动）

        Pro版限制：
        - 最大参与人数：500人（中大型活动）
        - 房间持续时间：24小时（全天活动）

        Max版限制：
        - 最大参与人数：2000人（大型活动，实际无限制）
        - 房间持续时间：72小时（多日活动）

        Args:
            user (User): 房间创建者，包含订阅等级信息

        Note:
            此方法会直接修改房间实例的max_participants和duration_hours字段
        """
        from ..user.models import User
        
        if user.subscription_level == User.SUBSCRIPTION_FREE:
            self.max_participants = 10
            self.duration_hours = 2
        elif user.subscription_level == User.SUBSCRIPTION_PRO:
            self.max_participants = 500
            self.duration_hours = 24
        elif user.subscription_level == User.SUBSCRIPTION_MAX:
            self.max_participants = 2000  # 实际上相当于无限制
            self.duration_hours = 72

    def get_participants(self):
        """获取房间参与者列表"""
        return self.room_participants.filter(is_active=True)

    def get_participant_count(self):
        """获取当前参与者数量"""
        return self.get_participants().count()

    def is_full(self):
        """检查房间是否已满"""
        return self.get_participant_count() >= self.max_participants

    def can_join(self, user):
        """检查用户是否可以加入房间"""
        if self.is_full():
            return False, "房间已满"
        
        if self.status not in [self.STATUS_OPEN, self.STATUS_WAITING, self.STATUS_READY]:
            return False, "房间状态不允许加入"
        
        # 检查用户是否已在房间中
        if self.room_participants.filter(user=user, is_active=True).exists():
            return False, "用户已在房间中"
        
        return True, ""

    def add_participant(self, user, role='participant'):
        """添加参与者到房间"""
        can_join, reason = self.can_join(user)
        if not can_join:
            raise ValueError(reason)
        
        participant, created = RoomParticipant.objects.get_or_create(
            room=self,
            user=user,
            defaults={'role': role, 'is_active': True}
        )
        
        if not created and not participant.is_active:
            participant.is_active = True
            participant.left_at = None
            participant.save()
        
        return participant

    def remove_participant(self, user):
        """从房间移除参与者"""
        try:
            participant = self.room_participants.get(user=user, is_active=True)
            participant.is_active = False
            participant.left_at = timezone.now()
            participant.save()
            return True
        except RoomParticipant.DoesNotExist:
            return False

    def get_current_step(self):
        """获取当前环节"""
        return self.current_event_step

    def get_next_step(self):
        """获取下一个环节"""
        try:
            return self.room_event_steps.filter(
                order__gt=self.current_step_order
            ).order_by('order').first()
        except:
            return None

    def advance_to_next_step(self):
        """推进到下一个环节"""
        next_step = self.get_next_step()
        if next_step:
            self.current_event_step = next_step
            self.current_step_order = next_step.order
            self.save()
            return next_step
        return None


class RoomParticipant(models.Model):
    """
    房间参与者模型

    管理用户在房间中的参与信息，包括：
    - 用户角色和权限
    - 参与状态和时间
    - 游戏得分和统计
    - 自定义数据存储
    """

    # 角色常量定义
    ROLE_HOST = 'host'
    ROLE_MODERATOR = 'moderator'
    ROLE_PARTICIPANT = 'participant'

    ROLE_CHOICES = [
        (ROLE_HOST, '房主'),
        (ROLE_MODERATOR, '管理员'),
        (ROLE_PARTICIPANT, '参与者'),
    ]

    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='room_participants')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='room_participations')
    joined_at = models.DateTimeField(auto_now_add=True, help_text='用户加入房间的时间')

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default=ROLE_PARTICIPANT,
        help_text='用户在房间中的角色'
    )

    # 游戏相关信息
    score = models.IntegerField(default=0, help_text='用户在房间中的总得分')

    # 用户状态（使用新的状态枚举）
    state = models.CharField(
        max_length=20,
        choices=UserState.choices,
        default=UserState.JOINED,
        help_text='用户在房间中的状态'
    )

    # 活跃状态信息
    is_active = models.BooleanField(default=True, help_text='用户是否仍在房间中（未退出）')
    left_at = models.DateTimeField(null=True, blank=True, help_text='用户离开房间的时间')

    # 扩展字段（为未来功能预留）
    custom_data = models.JSONField(default=dict, blank=True, help_text='自定义数据字段，用于存储特殊游戏状态等')

    class Meta:
        unique_together = ('room', 'user')
        ordering = ['-joined_at']
        verbose_name = '房间参与者'
        verbose_name_plural = '房间参与者'
        db_table = 'core_room_participant'

    def __str__(self):
        return f"{self.user.username} in {self.room.room_code} ({self.role})"

    def is_host(self):
        """检查是否为房主"""
        return self.role == self.ROLE_HOST

    def is_moderator(self):
        """检查是否为管理员"""
        return self.role == self.ROLE_MODERATOR

    def has_admin_permission(self):
        """检查是否有管理权限"""
        return self.role in [self.ROLE_HOST, self.ROLE_MODERATOR]

    def can_control_room(self):
        """检查是否可以控制房间"""
        return self.is_host()

    def add_score(self, points):
        """增加得分"""
        self.score += points
        self.save()

    def set_state(self, new_state):
        """设置用户状态"""
        if new_state in [choice[0] for choice in UserState.choices]:
            self.state = new_state
            self.save()


class RoomEventStep(models.Model):
    """
    房间环节模型 - 房间专属的环节副本

    这是房间-模板-环节系统重构的核心模型。
    每个房间都有自己独立的环节副本，实现完全的数据隔离。

    主要功能：
    - 存储房间专属的环节配置
    - 跟踪环节执行状态
    - 支持动态环节管理
    - 提供环节生命周期管理
    """

    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='room_event_steps')
    name = models.CharField(max_length=100, help_text='环节名称')
    order = models.PositiveIntegerField(help_text='环节顺序')
    step_type = models.CharField(max_length=50, help_text='环节类型')
    configuration = models.JSONField(default=dict, help_text='环节配置参数')
    duration = models.PositiveIntegerField(default=300, help_text='环节持续时间（秒）')

    # 环节状态跟踪
    is_completed = models.BooleanField(default=False, help_text='环节是否已完成')
    started_at = models.DateTimeField(null=True, blank=True, help_text='环节开始时间')
    completed_at = models.DateTimeField(null=True, blank=True, help_text='环节完成时间')

    # 环节结果数据
    result_data = models.JSONField(default=dict, blank=True, help_text='环节执行结果数据')

    class Meta:
        unique_together = ('room', 'order')
        ordering = ['room', 'order']
        verbose_name = '房间环节'
        verbose_name_plural = '房间环节'
        db_table = 'core_room_event_step'

    def __str__(self):
        return f"{self.room.room_code} - {self.name} (#{self.order})"

    def start(self):
        """开始环节"""
        if not self.started_at:
            self.started_at = timezone.now()
            self.save()

    def complete(self, result_data=None):
        """完成环节"""
        if not self.is_completed:
            self.is_completed = True
            self.completed_at = timezone.now()
            if result_data:
                self.result_data = result_data
            self.save()

    def get_duration_seconds(self):
        """获取环节持续时间（秒）"""
        return self.duration

    def get_remaining_time(self):
        """获取剩余时间（秒）"""
        if not self.started_at or self.is_completed:
            return 0
        
        elapsed = (timezone.now() - self.started_at).total_seconds()
        remaining = max(0, self.duration - elapsed)
        return int(remaining)

    def is_timeout(self):
        """检查是否超时"""
        return self.get_remaining_time() == 0 and not self.is_completed
