"""
房间数据访问层

提供房间相关的数据访问功能，包括：
- 房间CRUD操作
- 房间查询和过滤
- 参与者管理
- 统计和分析

遵循Repository模式，抽象数据访问逻辑。
"""

from typing import List, Optional, Dict, Any
from django.db.models import QuerySet, Q, Count, Avg
from django.utils import timezone
from datetime import timedelta

from ..logging.logger import get_logger, performance_monitor
from .models import Room, RoomParticipant, RoomEventStep, RoomState, UserState
from ..user.models import User

logger = get_logger(__name__)


class RoomRepository:
    """
    房间数据访问层

    提供房间相关的所有数据访问功能，包括：
    - 基础CRUD操作
    - 复杂查询和过滤
    - 统计分析
    - 性能优化查询
    """

    @performance_monitor('room_repository.get_by_id')
    def get_by_id(self, room_id: int) -> Optional[Room]:
        """
        根据ID获取房间

        Args:
            room_id: 房间ID

        Returns:
            Optional[Room]: 房间实例或None
        """
        try:
            return Room.objects.select_related('host', 'event_template', 'system_template').get(id=room_id)
        except Room.DoesNotExist:
            return None

    @performance_monitor('room_repository.get_by_code')
    def get_by_code(self, room_code: str) -> Optional[Room]:
        """
        根据房间码获取房间

        Args:
            room_code: 房间码

        Returns:
            Optional[Room]: 房间实例或None
        """
        try:
            return Room.objects.select_related('host', 'event_template', 'system_template').get(room_code=room_code)
        except Room.DoesNotExist:
            return None

    @performance_monitor('room_repository.create')
    def create(self, **kwargs) -> Room:
        """
        创建房间

        Args:
            **kwargs: 房间属性

        Returns:
            Room: 创建的房间实例
        """
        room = Room.objects.create(**kwargs)
        logger.debug(f"创建房间: {room.room_code}")
        return room

    @performance_monitor('room_repository.update')
    def update(self, room: Room, **kwargs) -> Room:
        """
        更新房间

        Args:
            room: 房间实例
            **kwargs: 更新的属性

        Returns:
            Room: 更新后的房间实例
        """
        for key, value in kwargs.items():
            setattr(room, key, value)
        room.save()
        logger.debug(f"更新房间: {room.room_code}")
        return room

    @performance_monitor('room_repository.delete')
    def delete(self, room: Room) -> bool:
        """
        删除房间

        Args:
            room: 房间实例

        Returns:
            bool: 是否删除成功
        """
        room_code = room.room_code
        room.delete()
        logger.debug(f"删除房间: {room_code}")
        return True

    @performance_monitor('room_repository.get_user_rooms')
    def get_user_rooms(
        self,
        user: User,
        status: str = None,
        limit: int = None,
        offset: int = 0
    ) -> QuerySet[Room]:
        """
        获取用户的房间列表

        Args:
            user: 用户
            status: 房间状态过滤
            limit: 限制数量
            offset: 偏移量

        Returns:
            QuerySet[Room]: 房间查询集
        """
        queryset = Room.objects.filter(
            room_participants__user=user,
            room_participants__is_active=True
        ).select_related('host').distinct().order_by('-created_at')

        if status:
            queryset = queryset.filter(status=status)

        if offset:
            queryset = queryset[offset:]

        if limit:
            queryset = queryset[:limit]

        return queryset

    @performance_monitor('room_repository.get_user_hosted_rooms')
    def get_user_hosted_rooms(
        self,
        user: User,
        status: str = None,
        limit: int = None
    ) -> QuerySet[Room]:
        """
        获取用户主持的房间列表

        Args:
            user: 用户
            status: 房间状态过滤
            limit: 限制数量

        Returns:
            QuerySet[Room]: 房间查询集
        """
        queryset = Room.objects.filter(host=user).order_by('-created_at')

        if status:
            queryset = queryset.filter(status=status)

        if limit:
            queryset = queryset[:limit]

        return queryset

    @performance_monitor('room_repository.get_user_active_rooms_count')
    def get_user_active_rooms_count(self, user: User) -> int:
        """
        获取用户活跃房间数量

        Args:
            user: 用户

        Returns:
            int: 活跃房间数量
        """
        active_statuses = [
            RoomState.SCHEDULED,
            RoomState.OPEN,
            RoomState.WAITING,
            RoomState.READY,
            RoomState.IN_PROGRESS
        ]
        
        return Room.objects.filter(
            host=user,
            status__in=active_statuses
        ).count()

    @performance_monitor('room_repository.get_rooms_by_status')
    def get_rooms_by_status(
        self,
        status: str,
        limit: int = None,
        include_expired: bool = False
    ) -> QuerySet[Room]:
        """
        根据状态获取房间列表

        Args:
            status: 房间状态
            limit: 限制数量
            include_expired: 是否包含过期房间

        Returns:
            QuerySet[Room]: 房间查询集
        """
        queryset = Room.objects.filter(status=status)

        if not include_expired:
            queryset = queryset.filter(
                Q(expires_at__isnull=True) | Q(expires_at__gt=timezone.now())
            )

        queryset = queryset.order_by('-created_at')

        if limit:
            queryset = queryset[:limit]

        return queryset

    @performance_monitor('room_repository.get_expired_rooms')
    def get_expired_rooms(self, grace_period_minutes: int = 15) -> QuerySet[Room]:
        """
        获取过期房间列表

        Args:
            grace_period_minutes: 宽限期（分钟）

        Returns:
            QuerySet[Room]: 过期房间查询集
        """
        cutoff_time = timezone.now() - timedelta(minutes=grace_period_minutes)
        
        return Room.objects.filter(
            expires_at__lt=cutoff_time,
            status__in=[RoomState.ENDED, RoomState.CLOSED]
        )

    @performance_monitor('room_repository.get_scheduled_rooms_to_activate')
    def get_scheduled_rooms_to_activate(self) -> QuerySet[Room]:
        """
        获取需要激活的预约房间

        Returns:
            QuerySet[Room]: 需要激活的房间查询集
        """
        return Room.objects.filter(
            status=RoomState.SCHEDULED,
            scheduled_start_time__lte=timezone.now()
        )

    @performance_monitor('room_repository.search_rooms')
    def search_rooms(
        self,
        query: str,
        user: User = None,
        status: str = None,
        limit: int = 20
    ) -> QuerySet[Room]:
        """
        搜索房间

        Args:
            query: 搜索关键词
            user: 搜索用户的房间
            status: 状态过滤
            limit: 限制数量

        Returns:
            QuerySet[Room]: 搜索结果
        """
        queryset = Room.objects.filter(
            Q(name__icontains=query) | Q(room_code__icontains=query)
        )

        if user:
            queryset = queryset.filter(
                room_participants__user=user,
                room_participants__is_active=True
            )

        if status:
            queryset = queryset.filter(status=status)

        return queryset.distinct().order_by('-created_at')[:limit]

    @performance_monitor('room_repository.get_room_statistics')
    def get_room_statistics(self, room: Room) -> Dict[str, Any]:
        """
        获取房间统计信息

        Args:
            room: 房间实例

        Returns:
            Dict[str, Any]: 统计信息
        """
        participants = room.room_participants.filter(is_active=True)
        
        return {
            'total_participants': participants.count(),
            'active_participants': participants.filter(state=UserState.PLAYING).count(),
            'ready_participants': participants.filter(state=UserState.READY).count(),
            'spectating_participants': participants.filter(state=UserState.SPECTATING).count(),
            'average_score': participants.aggregate(avg_score=Avg('score'))['avg_score'] or 0,
            'total_steps': room.room_event_steps.count(),
            'completed_steps': room.room_event_steps.filter(is_completed=True).count(),
            'current_step_order': room.current_step_order,
            'duration_minutes': self._calculate_room_duration(room),
        }

    @performance_monitor('room_repository.get_participant_statistics')
    def get_participant_statistics(self, user: User) -> Dict[str, Any]:
        """
        获取用户参与统计

        Args:
            user: 用户

        Returns:
            Dict[str, Any]: 参与统计
        """
        participations = RoomParticipant.objects.filter(user=user)
        
        return {
            'total_rooms': participations.count(),
            'hosted_rooms': participations.filter(role=RoomParticipant.ROLE_HOST).count(),
            'completed_rooms': participations.filter(room__status=RoomState.ENDED).count(),
            'total_score': participations.aggregate(total=Count('score'))['total'] or 0,
            'average_score': participations.aggregate(avg=Avg('score'))['avg'] or 0,
            'favorite_room_types': self._get_favorite_room_types(user),
        }

    def _calculate_room_duration(self, room: Room) -> int:
        """
        计算房间持续时间（分钟）

        Args:
            room: 房间实例

        Returns:
            int: 持续时间（分钟）
        """
        if room.status in [RoomState.ENDED, RoomState.CLOSED] and room.closed_at:
            duration = room.closed_at - room.created_at
        else:
            duration = timezone.now() - room.created_at
        
        return int(duration.total_seconds() / 60)

    def _get_favorite_room_types(self, user: User) -> List[str]:
        """
        获取用户偏好的房间类型

        Args:
            user: 用户

        Returns:
            List[str]: 房间类型列表
        """
        # 这里需要根据实际的模板系统实现
        # 暂时返回空列表
        return []


class RoomParticipantRepository:
    """
    房间参与者数据访问层

    提供参与者相关的数据访问功能。
    """

    @performance_monitor('participant_repository.get_by_room_and_user')
    def get_by_room_and_user(self, room: Room, user: User) -> Optional[RoomParticipant]:
        """
        获取房间中的特定参与者

        Args:
            room: 房间
            user: 用户

        Returns:
            Optional[RoomParticipant]: 参与者实例或None
        """
        try:
            return RoomParticipant.objects.get(room=room, user=user, is_active=True)
        except RoomParticipant.DoesNotExist:
            return None

    @performance_monitor('participant_repository.get_room_participants')
    def get_room_participants(
        self,
        room: Room,
        role: str = None,
        state: str = None,
        is_active: bool = True
    ) -> QuerySet[RoomParticipant]:
        """
        获取房间参与者列表

        Args:
            room: 房间
            role: 角色过滤
            state: 状态过滤
            is_active: 是否活跃

        Returns:
            QuerySet[RoomParticipant]: 参与者查询集
        """
        queryset = RoomParticipant.objects.filter(room=room, is_active=is_active)
        
        if role:
            queryset = queryset.filter(role=role)
        
        if state:
            queryset = queryset.filter(state=state)
        
        return queryset.select_related('user').order_by('joined_at')

    @performance_monitor('participant_repository.update_participant_state')
    def update_participant_state(
        self,
        participant: RoomParticipant,
        new_state: str
    ) -> RoomParticipant:
        """
        更新参与者状态

        Args:
            participant: 参与者实例
            new_state: 新状态

        Returns:
            RoomParticipant: 更新后的参与者
        """
        participant.state = new_state
        participant.save()
        logger.debug(f"更新参与者状态: {participant.user.username} -> {new_state}")
        return participant

    @performance_monitor('participant_repository.add_score')
    def add_score(self, participant: RoomParticipant, points: int) -> RoomParticipant:
        """
        为参与者添加得分

        Args:
            participant: 参与者实例
            points: 得分

        Returns:
            RoomParticipant: 更新后的参与者
        """
        participant.score += points
        participant.save()
        logger.debug(f"添加得分: {participant.user.username} +{points}")
        return participant
