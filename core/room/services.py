"""
房间业务逻辑服务

提供房间相关的业务逻辑处理，包括：
- 房间创建和管理
- 房间状态机控制
- 参与者管理
- 房间权限控制

遵循业务逻辑与数据访问分离原则。
"""

import random
import string
from typing import List, Optional, Dict, Any, Tuple
from django.utils import timezone
from django.db import transaction

from ..logging.logger import get_logger, performance_monitor, log_exceptions
from ..exceptions import (
    RoomNotFoundException,
    RoomFullException,
    RoomStateException,
    RoomPermissionException,
    RoomExpiredException,
    SubscriptionLimitExceededException,
    UserNotFoundException,
    ValidationException,
    BusinessLogicException
)
from .models import Room, RoomParticipant, RoomEventStep, RoomState, UserState
from .repositories import RoomRepository
from ..user.models import User

logger = get_logger(__name__)


class RoomService:
    """
    房间业务逻辑服务

    提供房间相关的所有业务逻辑处理，包括：
    - 房间生命周期管理
    - 状态机控制
    - 参与者管理
    - 权限验证
    - 订阅限制检查
    """

    def __init__(self, room_repository: RoomRepository = None):
        """
        初始化房间服务

        Args:
            room_repository: 房间数据访问层
        """
        self.room_repository = room_repository or RoomRepository()

    @performance_monitor('room_service.create_room')
    @log_exceptions('room_service')
    def create_room(
        self,
        host: User,
        name: str = None,
        event_template_id: int = None,
        system_template_id: int = None,
        scheduled_start_time: timezone.datetime = None,
        max_participants: int = None,
        duration_hours: int = None
    ) -> Room:
        """
        创建房间

        Args:
            host: 房主用户
            name: 房间名称
            event_template_id: 事件模板ID
            system_template_id: 系统模板ID
            scheduled_start_time: 预约开始时间
            max_participants: 最大参与人数
            duration_hours: 持续时间（小时）

        Returns:
            Room: 创建的房间实例

        Raises:
            SubscriptionLimitExceededException: 超出订阅限制
            ValidationException: 参数验证失败
        """
        # 检查用户房间创建限制
        self._check_room_creation_limits(host)

        # 生成房间码
        room_code = self._generate_room_code()

        # 确定房间状态
        initial_status = RoomState.SCHEDULED if scheduled_start_time else RoomState.OPEN

        # 计算过期时间
        expires_at = None
        if scheduled_start_time:
            expires_at = scheduled_start_time + timezone.timedelta(hours=duration_hours or 2)
        else:
            expires_at = timezone.now() + timezone.timedelta(hours=duration_hours or 2)

        with transaction.atomic():
            # 创建房间
            room = Room.objects.create(
                room_code=room_code,
                name=name or f"房间{room_code}",
                host=host,
                status=initial_status,
                scheduled_start_time=scheduled_start_time,
                expires_at=expires_at,
                event_template_id=event_template_id,
                system_template_id=system_template_id
            )

            # 设置订阅限制
            room.set_limits_by_subscription(host)
            if max_participants:
                room.max_participants = min(max_participants, room.max_participants)
            if duration_hours:
                room.duration_hours = min(duration_hours, room.duration_hours)
            room.save()

            # 添加房主为参与者
            self._add_host_as_participant(room, host)

            # 如果有模板，创建房间环节
            if event_template_id or system_template_id:
                self._create_room_steps_from_template(room, event_template_id, system_template_id)

            logger.info(
                f"创建房间成功",
                extra={
                    'room_id': room.id,
                    'room_code': room.room_code,
                    'host_id': host.id,
                    'status': room.status,
                    'max_participants': room.max_participants
                }
            )

            return room

    @performance_monitor('room_service.join_room')
    @log_exceptions('room_service')
    def join_room(self, room_code: str, user: User) -> RoomParticipant:
        """
        用户加入房间

        Args:
            room_code: 房间码
            user: 用户

        Returns:
            RoomParticipant: 参与者实例

        Raises:
            RoomNotFoundException: 房间不存在
            RoomFullException: 房间已满
            RoomStateException: 房间状态不允许加入
        """
        room = self.room_repository.get_by_code(room_code)
        if not room:
            raise RoomNotFoundException(f"房间不存在: {room_code}")

        # 检查房间状态
        if room.status not in [RoomState.OPEN, RoomState.WAITING, RoomState.READY]:
            raise RoomStateException(
                f"房间状态不允许加入: {room.status}",
                current_state=room.status,
                allowed_states=[RoomState.OPEN, RoomState.WAITING, RoomState.READY]
            )

        # 检查房间是否已满
        if room.is_full():
            raise RoomFullException(
                f"房间已满: {room.room_code}",
                current_count=room.get_participant_count(),
                max_count=room.max_participants
            )

        # 检查用户是否已在房间中
        existing_participant = room.room_participants.filter(
            user=user, is_active=True
        ).first()
        if existing_participant:
            return existing_participant

        with transaction.atomic():
            # 添加参与者
            participant = room.add_participant(user)

            # 更新房间状态
            self._update_room_state_after_join(room)

            logger.info(
                f"用户加入房间",
                extra={
                    'room_id': room.id,
                    'room_code': room.room_code,
                    'user_id': user.id,
                    'participant_count': room.get_participant_count()
                }
            )

            return participant

    @performance_monitor('room_service.leave_room')
    @log_exceptions('room_service')
    def leave_room(self, room_code: str, user: User) -> bool:
        """
        用户离开房间

        Args:
            room_code: 房间码
            user: 用户

        Returns:
            bool: 是否成功离开

        Raises:
            RoomNotFoundException: 房间不存在
        """
        room = self.room_repository.get_by_code(room_code)
        if not room:
            raise RoomNotFoundException(f"房间不存在: {room_code}")

        with transaction.atomic():
            # 移除参与者
            success = room.remove_participant(user)
            
            if success:
                # 如果是房主离开，转移房主权限
                if room.host == user:
                    self._transfer_host_role(room)

                # 更新房间状态
                self._update_room_state_after_leave(room)

                logger.info(
                    f"用户离开房间",
                    extra={
                        'room_id': room.id,
                        'room_code': room.room_code,
                        'user_id': user.id,
                        'participant_count': room.get_participant_count()
                    }
                )

            return success

    @performance_monitor('room_service.start_activity')
    @log_exceptions('room_service')
    def start_activity(self, room_code: str, user: User) -> Room:
        """
        开始房间活动

        Args:
            room_code: 房间码
            user: 操作用户

        Returns:
            Room: 更新后的房间

        Raises:
            RoomNotFoundException: 房间不存在
            RoomPermissionException: 权限不足
            RoomStateException: 房间状态不允许开始
        """
        room = self.room_repository.get_by_code(room_code)
        if not room:
            raise RoomNotFoundException(f"房间不存在: {room_code}")

        # 检查权限
        if not self._check_host_permission(room, user):
            raise RoomPermissionException(
                "只有房主可以开始活动",
                required_role='host',
                user_role=self._get_user_role_in_room(room, user)
            )

        # 检查房间状态
        if room.status != RoomState.READY:
            raise RoomStateException(
                f"房间状态不允许开始活动: {room.status}",
                current_state=room.status,
                required_state=RoomState.READY
            )

        with transaction.atomic():
            # 更新房间状态
            room.status = RoomState.IN_PROGRESS
            room.save()

            # 开始第一个环节
            first_step = room.room_event_steps.filter(order=1).first()
            if first_step:
                first_step.start()
                room.current_event_step = first_step
                room.current_step_order = 1
                room.save()

            logger.info(
                f"开始房间活动",
                extra={
                    'room_id': room.id,
                    'room_code': room.room_code,
                    'host_id': user.id,
                    'current_step': first_step.name if first_step else None
                }
            )

            return room

    def _check_room_creation_limits(self, user: User):
        """检查房间创建限制"""
        # 获取用户当前活跃房间数
        active_rooms_count = self.room_repository.get_user_active_rooms_count(user)
        
        # 根据订阅等级检查限制
        if user.subscription_level == User.SUBSCRIPTION_FREE:
            max_rooms = 5
        elif user.subscription_level == User.SUBSCRIPTION_PRO:
            max_rooms = 20
        else:  # MAX
            max_rooms = -1  # 无限制

        if max_rooms > 0 and active_rooms_count >= max_rooms:
            raise SubscriptionLimitExceededException(
                f"超出房间创建限制",
                current_level=user.subscription_level,
                limit_type='room_creation',
                current_usage=active_rooms_count,
                limit_value=max_rooms
            )

    def _generate_room_code(self) -> str:
        """生成房间码"""
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if not Room.objects.filter(room_code=code).exists():
                return code

    def _add_host_as_participant(self, room: Room, host: User):
        """添加房主为参与者"""
        RoomParticipant.objects.create(
            room=room,
            user=host,
            role=RoomParticipant.ROLE_HOST,
            state=UserState.JOINED
        )

    def _create_room_steps_from_template(
        self,
        room: Room,
        event_template_id: int = None,
        system_template_id: int = None
    ):
        """从模板创建房间环节"""
        # 这里需要根据实际的模板系统实现
        # 暂时创建一个示例环节
        RoomEventStep.objects.create(
            room=room,
            name="欢迎环节",
            order=1,
            step_type="welcome",
            configuration={"message": "欢迎来到团建活动！"},
            duration=300  # 5分钟
        )

    def _update_room_state_after_join(self, room: Room):
        """用户加入后更新房间状态"""
        if room.status == RoomState.OPEN:
            room.status = RoomState.WAITING
            room.save()
        elif room.status == RoomState.WAITING:
            # 检查是否满足开始条件
            if self._check_ready_conditions(room):
                room.status = RoomState.READY
                room.save()

    def _update_room_state_after_leave(self, room: Room):
        """用户离开后更新房间状态"""
        participant_count = room.get_participant_count()
        
        if participant_count == 0:
            room.status = RoomState.OPEN
            room.save()
        elif room.status == RoomState.READY and not self._check_ready_conditions(room):
            room.status = RoomState.WAITING
            room.save()

    def _check_ready_conditions(self, room: Room) -> bool:
        """检查房间是否满足开始条件"""
        # 至少需要2个参与者
        return room.get_participant_count() >= 2

    def _transfer_host_role(self, room: Room):
        """转移房主权限"""
        # 找到下一个管理员或参与者
        next_host = room.room_participants.filter(
            is_active=True
        ).exclude(
            user=room.host
        ).order_by('joined_at').first()

        if next_host:
            # 更新房间房主
            room.host = next_host.user
            room.save()
            
            # 更新参与者角色
            next_host.role = RoomParticipant.ROLE_HOST
            next_host.save()

            logger.info(
                f"转移房主权限",
                extra={
                    'room_id': room.id,
                    'new_host_id': next_host.user.id,
                    'old_host_id': room.host.id
                }
            )

    def _check_host_permission(self, room: Room, user: User) -> bool:
        """检查房主权限"""
        return room.host == user

    def _get_user_role_in_room(self, room: Room, user: User) -> str:
        """获取用户在房间中的角色"""
        participant = room.room_participants.filter(
            user=user, is_active=True
        ).first()
        return participant.role if participant else 'none'
