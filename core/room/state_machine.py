"""
房间状态机

实现房间状态的严格管理，包括：
- 状态转换规则
- 状态验证
- 状态事件处理
- 自动状态管理

遵循状态机模式，确保房间状态的一致性。
"""

from typing import Dict, List, Callable, Optional
from enum import Enum
from django.utils import timezone

from ..logging.logger import get_logger
from ..exceptions import RoomStateException, BusinessLogicException
from .models import Room, RoomState

logger = get_logger(__name__)


class StateTransition:
    """
    状态转换描述符

    定义状态转换的条件和处理逻辑
    """

    def __init__(
        self,
        from_state: str,
        to_state: str,
        condition: Callable[[Room], bool] = None,
        action: Callable[[Room], None] = None,
        description: str = ""
    ):
        """
        初始化状态转换

        Args:
            from_state: 源状态
            to_state: 目标状态
            condition: 转换条件函数
            action: 转换动作函数
            description: 转换描述
        """
        self.from_state = from_state
        self.to_state = to_state
        self.condition = condition or (lambda room: True)
        self.action = action or (lambda room: None)
        self.description = description

    def can_transition(self, room: Room) -> bool:
        """
        检查是否可以转换

        Args:
            room: 房间实例

        Returns:
            bool: 是否可以转换
        """
        return self.condition(room)

    def execute(self, room: Room):
        """
        执行状态转换

        Args:
            room: 房间实例
        """
        self.action(room)


class RoomStateMachine:
    """
    房间状态机

    管理房间的状态转换和生命周期，确保状态变化的一致性和正确性。

    状态转换图：
    SCHEDULED -> OPEN -> WAITING -> READY -> IN_PROGRESS -> ENDED -> CLOSED
                  |         |        |          |
                  v         v        v          v
                ENDED    ENDED    ENDED      ENDED
    """

    def __init__(self):
        """初始化状态机"""
        self._transitions: Dict[str, List[StateTransition]] = {}
        self._setup_transitions()

    def _setup_transitions(self):
        """设置状态转换规则"""
        # SCHEDULED -> OPEN: 到达预定开始时间
        self._add_transition(
            RoomState.SCHEDULED,
            RoomState.OPEN,
            condition=self._is_scheduled_time_reached,
            action=self._activate_scheduled_room,
            description="预定时间到达，激活房间"
        )

        # OPEN -> WAITING: 有人加入房间
        self._add_transition(
            RoomState.OPEN,
            RoomState.WAITING,
            condition=self._has_participants,
            description="有用户加入房间"
        )

        # OPEN -> ENDED: 超过房间结束时间
        self._add_transition(
            RoomState.OPEN,
            RoomState.ENDED,
            condition=self._is_expired,
            action=self._end_room,
            description="房间超时结束"
        )

        # WAITING -> READY: 满足开始条件
        self._add_transition(
            RoomState.WAITING,
            RoomState.READY,
            condition=self._meets_start_conditions,
            description="满足活动开始条件"
        )

        # WAITING -> OPEN: 最后一个人离开房间
        self._add_transition(
            RoomState.WAITING,
            RoomState.OPEN,
            condition=self._no_participants,
            description="所有用户离开房间"
        )

        # WAITING -> ENDED: 超过房间结束时间
        self._add_transition(
            RoomState.WAITING,
            RoomState.ENDED,
            condition=self._is_expired,
            action=self._end_room,
            description="房间超时结束"
        )

        # READY -> IN_PROGRESS: 房主开始活动
        self._add_transition(
            RoomState.READY,
            RoomState.IN_PROGRESS,
            action=self._start_activity,
            description="房主开始活动"
        )

        # READY -> WAITING: 开始条件被破坏
        self._add_transition(
            RoomState.READY,
            RoomState.WAITING,
            condition=self._start_conditions_broken,
            description="开始条件不再满足"
        )

        # READY -> ENDED: 超过房间结束时间
        self._add_transition(
            RoomState.READY,
            RoomState.ENDED,
            condition=self._is_expired,
            action=self._end_room,
            description="房间超时结束"
        )

        # IN_PROGRESS -> READY: 环节结束
        self._add_transition(
            RoomState.IN_PROGRESS,
            RoomState.READY,
            condition=self._current_step_completed,
            action=self._prepare_next_step,
            description="当前环节完成"
        )

        # IN_PROGRESS -> ENDED: 所有环节完成或超时
        self._add_transition(
            RoomState.IN_PROGRESS,
            RoomState.ENDED,
            condition=lambda room: self._all_steps_completed(room) or self._is_expired(room),
            action=self._end_room,
            description="活动完成或超时"
        )

        # ENDED -> CLOSED: 超过结束时间15分钟
        self._add_transition(
            RoomState.ENDED,
            RoomState.CLOSED,
            condition=self._is_cleanup_time,
            action=self._close_room,
            description="房间清理时间到达"
        )

    def _add_transition(
        self,
        from_state: str,
        to_state: str,
        condition: Callable[[Room], bool] = None,
        action: Callable[[Room], None] = None,
        description: str = ""
    ):
        """添加状态转换"""
        if from_state not in self._transitions:
            self._transitions[from_state] = []

        transition = StateTransition(from_state, to_state, condition, action, description)
        self._transitions[from_state].append(transition)

    def can_transition_to(self, room: Room, target_state: str) -> bool:
        """
        检查是否可以转换到目标状态

        Args:
            room: 房间实例
            target_state: 目标状态

        Returns:
            bool: 是否可以转换
        """
        current_state = room.status
        if current_state not in self._transitions:
            return False

        for transition in self._transitions[current_state]:
            if transition.to_state == target_state:
                return transition.can_transition(room)

        return False

    def transition_to(self, room: Room, target_state: str, force: bool = False) -> bool:
        """
        转换到目标状态

        Args:
            room: 房间实例
            target_state: 目标状态
            force: 是否强制转换

        Returns:
            bool: 是否转换成功

        Raises:
            RoomStateException: 状态转换失败
        """
        current_state = room.status

        if current_state == target_state:
            return True

        if not force and not self.can_transition_to(room, target_state):
            raise RoomStateException(
                f"无法从状态 {current_state} 转换到 {target_state}",
                current_state=current_state,
                target_state=target_state
            )

        # 执行转换
        if current_state in self._transitions:
            for transition in self._transitions[current_state]:
                if transition.to_state == target_state:
                    if force or transition.can_transition(room):
                        transition.execute(room)
                        room.status = target_state
                        room.save()

                        logger.info(
                            f"房间状态转换: {current_state} -> {target_state}",
                            extra={
                                'room_id': room.id,
                                'room_code': room.room_code,
                                'transition_description': transition.description
                            }
                        )
                        return True

        if force:
            # 强制转换
            room.status = target_state
            room.save()
            logger.warning(
                f"强制房间状态转换: {current_state} -> {target_state}",
                extra={'room_id': room.id, 'room_code': room.room_code}
            )
            return True

        return False

    def get_available_transitions(self, room: Room) -> List[str]:
        """
        获取可用的状态转换

        Args:
            room: 房间实例

        Returns:
            List[str]: 可转换的状态列表
        """
        current_state = room.status
        available_states = []

        if current_state in self._transitions:
            for transition in self._transitions[current_state]:
                if transition.can_transition(room):
                    available_states.append(transition.to_state)

        return available_states

    def auto_transition(self, room: Room) -> bool:
        """
        自动状态转换

        Args:
            room: 房间实例

        Returns:
            bool: 是否发生了转换
        """
        available_transitions = self.get_available_transitions(room)
        
        if available_transitions:
            # 选择第一个可用的转换
            target_state = available_transitions[0]
            return self.transition_to(room, target_state)

        return False

    # 状态转换条件函数
    def _is_scheduled_time_reached(self, room: Room) -> bool:
        """检查是否到达预定时间"""
        return (room.scheduled_start_time and 
                timezone.now() >= room.scheduled_start_time)

    def _has_participants(self, room: Room) -> bool:
        """检查是否有参与者"""
        return room.get_participant_count() > 0

    def _no_participants(self, room: Room) -> bool:
        """检查是否没有参与者"""
        return room.get_participant_count() == 0

    def _meets_start_conditions(self, room: Room) -> bool:
        """检查是否满足开始条件"""
        # 至少需要2个参与者
        return room.get_participant_count() >= 2

    def _start_conditions_broken(self, room: Room) -> bool:
        """检查开始条件是否被破坏"""
        return not self._meets_start_conditions(room)

    def _is_expired(self, room: Room) -> bool:
        """检查是否过期"""
        return (room.expires_at and 
                timezone.now() > room.expires_at)

    def _current_step_completed(self, room: Room) -> bool:
        """检查当前环节是否完成"""
        current_step = room.get_current_step()
        return current_step and current_step.is_completed

    def _all_steps_completed(self, room: Room) -> bool:
        """检查所有环节是否完成"""
        total_steps = room.room_event_steps.count()
        completed_steps = room.room_event_steps.filter(is_completed=True).count()
        return total_steps > 0 and completed_steps >= total_steps

    def _is_cleanup_time(self, room: Room) -> bool:
        """检查是否到达清理时间"""
        if not room.closed_at:
            return False
        
        cleanup_time = room.closed_at + timezone.timedelta(minutes=15)
        return timezone.now() >= cleanup_time

    # 状态转换动作函数
    def _activate_scheduled_room(self, room: Room):
        """激活预约房间"""
        logger.info(f"激活预约房间: {room.room_code}")

    def _start_activity(self, room: Room):
        """开始活动"""
        first_step = room.room_event_steps.filter(order=1).first()
        if first_step:
            first_step.start()
            room.current_event_step = first_step
            room.current_step_order = 1
            room.save()

    def _prepare_next_step(self, room: Room):
        """准备下一个环节"""
        next_step = room.get_next_step()
        if next_step:
            room.current_event_step = next_step
            room.current_step_order = next_step.order
            room.save()

    def _end_room(self, room: Room):
        """结束房间"""
        room.closed_at = timezone.now()
        room.save()
        logger.info(f"房间结束: {room.room_code}")

    def _close_room(self, room: Room):
        """关闭房间"""
        # 这里可以添加清理逻辑
        logger.info(f"房间关闭: {room.room_code}")


# 全局状态机实例
_state_machine = None


def get_room_state_machine() -> RoomStateMachine:
    """
    获取房间状态机实例

    Returns:
        RoomStateMachine: 状态机实例
    """
    global _state_machine
    if _state_machine is None:
        _state_machine = RoomStateMachine()
    return _state_machine
