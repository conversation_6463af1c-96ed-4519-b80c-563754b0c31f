"""
用户模型模块

定义用户相关的数据模型，包括：
- 扩展的用户模型
- 用户订阅等级管理
- 用户权限和角色定义

技术特性：
- 基于Django AbstractUser的扩展
- 支持订阅等级的权限控制
- 完整的用户权限体系
"""

from django.db import models
from django.contrib.auth.models import AbstractUser, Group
from django.conf import settings


class User(AbstractUser):
    """
    扩展的用户模型

    在Django默认用户模型基础上添加订阅功能：
    - 订阅等级管理
    - 基于订阅等级的功能权限控制
    """

    # 订阅等级常量定义
    SUBSCRIPTION_FREE = 'Free'  # 免费版：基础功能
    SUBSCRIPTION_PRO = 'Pro'    # Pro版：高级功能
    SUBSCRIPTION_MAX = 'Max'    # Max版：全功能

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, 'Free'),
        (SUBSCRIPTION_PRO, 'Pro'),
        (SUBSCRIPTION_MAX, 'Max'),
    ]

    # 用户订阅等级字段
    subscription_level = models.CharField(
        max_length=10,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
        help_text='用户订阅等级，决定可用功能范围'
    )

    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text=('The groups this user belongs to. A user will get all permissions '
                   'granted to each of their groups.'),
        related_name="core_user_set",
        related_query_name="user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_permissions_set",
        related_query_name="user",
    )

    def __str__(self):
        return f"{self.username} ({self.subscription_level})"

    class Meta:
        db_table = 'core_user'
        verbose_name = '用户'
        verbose_name_plural = '用户'

    # 用户组权限检查方法
    def is_in_group(self, group_name):
        """检查用户是否属于指定组"""
        return self.groups.filter(name=group_name).exists()

    def is_administrator(self):
        """检查用户是否为管理员（替代is_superuser）"""
        return self.is_in_group('Super Administrators') or self.is_in_group('Administrators')

    def is_staff_member(self):
        """检查用户是否为工作人员（替代is_staff）"""
        return self.is_administrator() or self.is_in_group('Staff')

    def get_user_role(self):
        """获取用户的主要角色"""
        if self.is_in_group('Super Administrators'):
            return 'super_admin'
        elif self.is_in_group('Administrators'):
            return 'admin'
        elif self.is_in_group('Staff'):
            return 'staff'
        else:
            return 'user'

    def get_daily_room_creation_limit(self):
        """
        根据用户订阅等级获取每日房间创建限制

        不同订阅等级的房间创建限制：
        - Free版：每日最多5个房间
        - Pro版：每日最多20个房间
        - Max版：无限制创建

        Returns:
            int: 每日可创建房间数量，-1表示无限制

        Note:
            此限制用于防止滥用和控制服务器资源消耗
        """
        if self.subscription_level == self.SUBSCRIPTION_FREE:
            return 5
        elif self.subscription_level == self.SUBSCRIPTION_PRO:
            return 20
        elif self.subscription_level == self.SUBSCRIPTION_MAX:
            return -1  # 无限制
        else:
            return 5  # 默认为免费版限制

    def check_daily_room_creation_limit(self):
        """
        检查用户今日是否还能创建房间

        此方法会：
        1. 获取用户的每日房间创建限制
        2. 统计用户今日已创建的房间数量
        3. 计算剩余可创建数量
        4. 返回检查结果和相关信息

        算法逻辑：
        - 对于Max用户：直接返回无限制
        - 对于其他用户：查询今日00:00:00到明日00:00:00之间创建的房间
        - 计算剩余额度：每日限制 - 今日已创建数量

        Returns:
            tuple: (bool, str, int) - (是否可以创建, 错误信息, 剩余可创建数量)

        Note:
            使用Django应用注册表避免循环导入问题
        """
        from django.utils import timezone
        from datetime import timedelta
        from django.apps import apps

        # 获取用户的每日限制
        daily_limit = self.get_daily_room_creation_limit()

        # Max用户无限制
        if daily_limit == -1:
            return True, "", -1

        # 计算今日时间范围（00:00:00 到 23:59:59）
        today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)

        # 使用Django应用注册表避免循环导入
        Room = apps.get_model('core', 'Room')

        # 查询今日已创建的房间数量
        today_created_count = Room.objects.filter(
            host=self,
            created_at__gte=today_start,
            created_at__lt=today_end
        ).count()

        # 计算剩余可创建数量
        remaining = daily_limit - today_created_count

        if remaining <= 0:
            return False, f"您今日已达到房间创建限制（{daily_limit}个），请明日再试或升级订阅", 0

        return True, "", remaining


class CheckIn(models.Model):
    """
    用户签到模型

    记录用户的签到信息，支持签到功能
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='checkins')
    check_in_date = models.DateField(auto_now_add=True, help_text='签到日期')
    check_in_time = models.DateTimeField(auto_now_add=True, help_text='签到时间')
    location = models.CharField(max_length=255, blank=True, null=True, help_text='签到地点')
    notes = models.TextField(blank=True, null=True, help_text='签到备注')

    class Meta:
        unique_together = ('user', 'check_in_date')
        ordering = ['-check_in_date']
        verbose_name = '用户签到'
        verbose_name_plural = '用户签到'

    def __str__(self):
        return f"{self.user.username} - {self.check_in_date}"
