"""
用户数据访问层

实现Repository模式，抽象用户数据访问逻辑，包括：
- 用户CRUD操作
- 用户查询和过滤
- 用户统计数据
- 数据库事务管理

遵循Repository模式，提供数据访问的抽象接口。
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from django.db import transaction
from django.contrib.auth import get_user_model
from django.db.models import Count, Q

from .models import User, CheckIn
from ..exceptions.user_exceptions import UserNotFoundException
from ..logging.logger import get_logger

logger = get_logger(__name__)


class UserRepository:
    """
    用户数据访问仓库

    提供用户相关的所有数据访问操作，包括：
    - 基础CRUD操作
    - 复杂查询和统计
    - 事务管理
    - 数据验证
    """

    def __init__(self):
        """初始化用户仓库"""
        self.model = get_user_model()

    def get_by_id(self, user_id: int) -> Optional[User]:
        """
        根据ID获取用户

        Args:
            user_id: 用户ID

        Returns:
            User: 用户对象，不存在时返回None
        """
        try:
            return self.model.objects.get(id=user_id)
        except self.model.DoesNotExist:
            logger.warning(f"用户ID {user_id} 不存在")
            return None
        except Exception as e:
            logger.error(f"获取用户ID {user_id} 失败: {str(e)}")
            raise

    def get_by_username(self, username: str) -> Optional[User]:
        """
        根据用户名获取用户

        Args:
            username: 用户名

        Returns:
            User: 用户对象，不存在时返回None
        """
        try:
            return self.model.objects.get(username=username)
        except self.model.DoesNotExist:
            logger.warning(f"用户名 {username} 不存在")
            return None
        except Exception as e:
            logger.error(f"获取用户名 {username} 失败: {str(e)}")
            raise

    def get_by_email(self, email: str) -> Optional[User]:
        """
        根据邮箱获取用户

        Args:
            email: 邮箱地址

        Returns:
            User: 用户对象，不存在时返回None
        """
        try:
            return self.model.objects.get(email=email)
        except self.model.DoesNotExist:
            logger.warning(f"邮箱 {email} 不存在")
            return None
        except Exception as e:
            logger.error(f"获取邮箱 {email} 失败: {str(e)}")
            raise

    def exists_by_username(self, username: str) -> bool:
        """
        检查用户名是否存在

        Args:
            username: 用户名

        Returns:
            bool: 是否存在
        """
        try:
            return self.model.objects.filter(username=username).exists()
        except Exception as e:
            logger.error(f"检查用户名 {username} 是否存在失败: {str(e)}")
            raise

    def exists_by_email(self, email: str) -> bool:
        """
        检查邮箱是否存在

        Args:
            email: 邮箱地址

        Returns:
            bool: 是否存在
        """
        try:
            return self.model.objects.filter(email=email).exists()
        except Exception as e:
            logger.error(f"检查邮箱 {email} 是否存在失败: {str(e)}")
            raise

    @transaction.atomic
    def create_user(self, **user_data) -> User:
        """
        创建新用户

        Args:
            **user_data: 用户数据

        Returns:
            User: 创建的用户对象
        """
        try:
            password = user_data.pop('password', None)
            user = self.model(**user_data)
            
            if password:
                user.set_password(password)
            
            user.full_clean()  # 验证数据
            user.save()
            
            logger.info(f"用户创建成功: {user.username}")
            return user
            
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            raise

    @transaction.atomic
    def save(self, user: User) -> User:
        """
        保存用户

        Args:
            user: 用户对象

        Returns:
            User: 保存后的用户对象
        """
        try:
            user.full_clean()  # 验证数据
            user.save()
            logger.debug(f"用户 {user.username} 保存成功")
            return user
        except Exception as e:
            logger.error(f"保存用户 {user.username} 失败: {str(e)}")
            raise

    @transaction.atomic
    def delete(self, user: User) -> bool:
        """
        删除用户

        Args:
            user: 用户对象

        Returns:
            bool: 是否删除成功
        """
        try:
            username = user.username
            user.delete()
            logger.info(f"用户 {username} 删除成功")
            return True
        except Exception as e:
            logger.error(f"删除用户 {user.username} 失败: {str(e)}")
            raise

    def get_users_by_subscription_level(self, subscription_level: str) -> List[User]:
        """
        根据订阅等级获取用户列表

        Args:
            subscription_level: 订阅等级

        Returns:
            List[User]: 用户列表
        """
        try:
            return list(self.model.objects.filter(subscription_level=subscription_level))
        except Exception as e:
            logger.error(f"获取订阅等级 {subscription_level} 用户列表失败: {str(e)}")
            raise

    def get_active_users(self, days: int = 30) -> List[User]:
        """
        获取活跃用户列表

        Args:
            days: 活跃天数阈值

        Returns:
            List[User]: 活跃用户列表
        """
        try:
            from django.utils import timezone
            from datetime import timedelta
            
            cutoff_date = timezone.now() - timedelta(days=days)
            return list(self.model.objects.filter(last_login__gte=cutoff_date))
        except Exception as e:
            logger.error(f"获取活跃用户列表失败: {str(e)}")
            raise

    def get_today_room_count(self, user: User, today_start: datetime, today_end: datetime) -> int:
        """
        获取用户今日创建的房间数量

        Args:
            user: 用户对象
            today_start: 今日开始时间
            today_end: 今日结束时间

        Returns:
            int: 今日创建的房间数量
        """
        try:
            from django.apps import apps
            Room = apps.get_model('core', 'Room')
            
            return Room.objects.filter(
                host=user,
                created_at__gte=today_start,
                created_at__lt=today_end
            ).count()
        except Exception as e:
            logger.error(f"获取用户 {user.username} 今日房间数量失败: {str(e)}")
            raise

    def get_total_rooms_created(self, user: User) -> int:
        """
        获取用户创建的房间总数

        Args:
            user: 用户对象

        Returns:
            int: 创建的房间总数
        """
        try:
            from django.apps import apps
            Room = apps.get_model('core', 'Room')
            
            return Room.objects.filter(host=user).count()
        except Exception as e:
            logger.error(f"获取用户 {user.username} 创建房间总数失败: {str(e)}")
            raise

    def get_total_rooms_joined(self, user: User) -> int:
        """
        获取用户参与的房间总数

        Args:
            user: 用户对象

        Returns:
            int: 参与的房间总数
        """
        try:
            from django.apps import apps
            RoomParticipant = apps.get_model('core', 'RoomParticipant')
            
            return RoomParticipant.objects.filter(user=user).count()
        except Exception as e:
            logger.error(f"获取用户 {user.username} 参与房间总数失败: {str(e)}")
            raise

    def get_user_statistics(self, user: User) -> Dict[str, Any]:
        """
        获取用户统计信息

        Args:
            user: 用户对象

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            stats = {
                'total_rooms_created': self.get_total_rooms_created(user),
                'total_rooms_joined': self.get_total_rooms_joined(user),
                'total_check_ins': CheckIn.objects.filter(user=user).count(),
                'subscription_level': user.subscription_level,
                'is_active': user.is_active,
                'date_joined': user.date_joined,
                'last_login': user.last_login,
            }
            
            logger.debug(f"获取用户 {user.username} 统计信息成功")
            return stats
        except Exception as e:
            logger.error(f"获取用户 {user.username} 统计信息失败: {str(e)}")
            raise

    def search_users(self, query: str, limit: int = 50) -> List[User]:
        """
        搜索用户

        Args:
            query: 搜索关键词
            limit: 结果限制数量

        Returns:
            List[User]: 搜索结果
        """
        try:
            return list(
                self.model.objects.filter(
                    Q(username__icontains=query) |
                    Q(email__icontains=query) |
                    Q(first_name__icontains=query) |
                    Q(last_name__icontains=query)
                )[:limit]
            )
        except Exception as e:
            logger.error(f"搜索用户失败: {str(e)}")
            raise
