"""
用户服务层

提供用户相关的业务逻辑服务，包括：
- 用户注册和认证
- 用户权限管理
- 订阅等级验证
- 用户行为分析

遵循单一职责原则，只处理用户相关的业务逻辑。
"""

from typing import Tuple, Optional
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import authenticate
from django.core.exceptions import ValidationError

from .models import User, CheckIn
from .repositories import UserRepository
from ..exceptions.user_exceptions import (
    UserNotFoundException,
    InvalidCredentialsException,
    SubscriptionLimitExceededException
)
from ..logging.logger import get_logger

logger = get_logger(__name__)


class UserService:
    """
    用户业务逻辑服务类

    提供用户相关的所有业务操作，包括：
    - 用户认证和授权
    - 订阅等级管理
    - 用户权限验证
    - 用户行为记录
    """

    def __init__(self, user_repository: UserRepository = None):
        """
        初始化用户服务

        Args:
            user_repository: 用户数据访问层，支持依赖注入
        """
        self.user_repository = user_repository or UserRepository()

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        用户认证

        Args:
            username: 用户名
            password: 密码

        Returns:
            User: 认证成功的用户对象

        Raises:
            InvalidCredentialsException: 认证失败
        """
        try:
            user = authenticate(username=username, password=password)
            if user is None:
                logger.warning(f"用户认证失败: {username}")
                raise InvalidCredentialsException("用户名或密码错误")

            logger.info(f"用户认证成功: {username}")
            return user

        except Exception as e:
            logger.error(f"用户认证异常: {username}, 错误: {str(e)}")
            raise

    def create_user(self, username: str, email: str, password: str, **kwargs) -> User:
        """
        创建新用户

        Args:
            username: 用户名
            email: 邮箱
            password: 密码
            **kwargs: 其他用户属性

        Returns:
            User: 创建的用户对象

        Raises:
            ValidationError: 用户数据验证失败
        """
        try:
            # 检查用户名是否已存在
            if self.user_repository.exists_by_username(username):
                raise ValidationError(f"用户名 {username} 已存在")

            # 检查邮箱是否已存在
            if self.user_repository.exists_by_email(email):
                raise ValidationError(f"邮箱 {email} 已被使用")

            # 创建用户
            user_data = {
                'username': username,
                'email': email,
                'password': password,
                **kwargs
            }

            user = self.user_repository.create_user(**user_data)
            logger.info(f"用户创建成功: {username}")
            return user

        except Exception as e:
            logger.error(f"用户创建失败: {username}, 错误: {str(e)}")
            raise

    def get_user_by_id(self, user_id: int) -> User:
        """
        根据ID获取用户

        Args:
            user_id: 用户ID

        Returns:
            User: 用户对象

        Raises:
            UserNotFoundException: 用户不存在
        """
        user = self.user_repository.get_by_id(user_id)
        if user is None:
            raise UserNotFoundException(f"用户ID {user_id} 不存在")
        return user

    def check_subscription_permission(self, user: User, required_level: str) -> bool:
        """
        检查用户订阅权限

        Args:
            user: 用户对象
            required_level: 所需的订阅等级

        Returns:
            bool: 是否有权限
        """
        subscription_hierarchy = {
            User.SUBSCRIPTION_FREE: 0,
            User.SUBSCRIPTION_PRO: 1,
            User.SUBSCRIPTION_MAX: 2,
        }

        user_level = subscription_hierarchy.get(user.subscription_level, 0)
        required_level_value = subscription_hierarchy.get(required_level, 0)

        has_permission = user_level >= required_level_value
        
        logger.debug(f"用户 {user.username} 订阅权限检查: "
                    f"当前等级 {user.subscription_level}, "
                    f"所需等级 {required_level}, "
                    f"结果 {has_permission}")

        return has_permission

    def check_daily_room_creation_limit(self, user: User) -> Tuple[bool, str, int]:
        """
        检查用户每日房间创建限制

        Args:
            user: 用户对象

        Returns:
            tuple: (是否可以创建, 错误信息, 剩余可创建数量)
        """
        try:
            # 获取用户的每日限制
            daily_limit = user.get_daily_room_creation_limit()

            # Max用户无限制
            if daily_limit == -1:
                return True, "", -1

            # 计算今日时间范围
            today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)

            # 查询今日已创建的房间数量
            today_created_count = self.user_repository.get_today_room_count(
                user, today_start, today_end
            )

            # 计算剩余可创建数量
            remaining = daily_limit - today_created_count

            if remaining <= 0:
                error_msg = f"您今日已达到房间创建限制（{daily_limit}个），请明日再试或升级订阅"
                logger.warning(f"用户 {user.username} 达到每日房间创建限制")
                return False, error_msg, 0

            return True, "", remaining

        except Exception as e:
            logger.error(f"检查用户 {user.username} 每日房间创建限制失败: {str(e)}")
            raise

    def upgrade_subscription(self, user: User, new_level: str) -> User:
        """
        升级用户订阅等级

        Args:
            user: 用户对象
            new_level: 新的订阅等级

        Returns:
            User: 更新后的用户对象

        Raises:
            ValidationError: 订阅等级无效
        """
        valid_levels = [User.SUBSCRIPTION_FREE, User.SUBSCRIPTION_PRO, User.SUBSCRIPTION_MAX]
        if new_level not in valid_levels:
            raise ValidationError(f"无效的订阅等级: {new_level}")

        old_level = user.subscription_level
        user.subscription_level = new_level
        user = self.user_repository.save(user)

        logger.info(f"用户 {user.username} 订阅等级升级: {old_level} -> {new_level}")
        return user

    def record_check_in(self, user: User, location: str = None, notes: str = None) -> CheckIn:
        """
        记录用户签到

        Args:
            user: 用户对象
            location: 签到地点
            notes: 签到备注

        Returns:
            CheckIn: 签到记录

        Raises:
            ValidationError: 今日已签到
        """
        try:
            # 检查今日是否已签到
            today = timezone.now().date()
            if CheckIn.objects.filter(user=user, check_in_date=today).exists():
                raise ValidationError("今日已签到")

            # 创建签到记录
            check_in = CheckIn.objects.create(
                user=user,
                location=location,
                notes=notes
            )

            logger.info(f"用户 {user.username} 签到成功")
            return check_in

        except Exception as e:
            logger.error(f"用户 {user.username} 签到失败: {str(e)}")
            raise

    def get_user_statistics(self, user: User) -> dict:
        """
        获取用户统计信息

        Args:
            user: 用户对象

        Returns:
            dict: 用户统计数据
        """
        try:
            stats = {
                'total_rooms_created': self.user_repository.get_total_rooms_created(user),
                'total_rooms_joined': self.user_repository.get_total_rooms_joined(user),
                'total_check_ins': CheckIn.objects.filter(user=user).count(),
                'subscription_level': user.subscription_level,
                'daily_room_limit': user.get_daily_room_creation_limit(),
                'account_created': user.date_joined,
                'last_login': user.last_login,
            }

            logger.debug(f"获取用户 {user.username} 统计信息成功")
            return stats

        except Exception as e:
            logger.error(f"获取用户 {user.username} 统计信息失败: {str(e)}")
            raise
