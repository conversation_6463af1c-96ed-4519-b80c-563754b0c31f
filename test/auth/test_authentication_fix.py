"""
认证系统修复测试
测试JWT token管理、刷新机制和存储一致性
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
import json

User = get_user_model()


class AuthenticationFixTest(TestCase):
    """认证系统修复测试类"""
    
    def setUp(self):
        """测试初始化"""
        self.client = APIClient()
        self.test_user_data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        self.user = User.objects.create_user(**self.test_user_data)
        
    def test_token_storage_consistency(self):
        """测试token存储键的一致性"""
        # 测试登录获取token
        response = self.client.post('/api/users/login/', self.test_user_data)
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        assert 'refresh' in response.data
        
        # 验证token格式
        access_token = response.data['access']
        refresh_token = response.data['refresh']
        
        assert isinstance(access_token, str)
        assert isinstance(refresh_token, str)
        assert len(access_token) > 0
        assert len(refresh_token) > 0
        
    def test_token_refresh_endpoint(self):
        """测试token刷新端点"""
        # 获取初始token
        refresh = RefreshToken.for_user(self.user)
        
        # 测试token刷新
        response = self.client.post('/api/users/token/refresh/', {
            'refresh': str(refresh)
        })
        
        assert response.status_code == status.HTTP_200_OK
        assert 'access' in response.data
        
        # 验证新token有效性
        new_access_token = response.data['access']
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {new_access_token}')
        
        # 测试使用新token访问受保护端点
        health_response = self.client.get('/api/health-check/')
        assert health_response.status_code == status.HTTP_200_OK
        
    def test_token_refresh_with_invalid_token(self):
        """测试使用无效token刷新"""
        response = self.client.post('/api/users/token/refresh/', {
            'refresh': 'invalid_token'
        })
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    def test_token_refresh_with_expired_token(self):
        """测试使用过期token刷新"""
        # 创建一个已过期的refresh token
        from datetime import timedelta
        refresh = RefreshToken.for_user(self.user)
        # 手动设置过期时间为过去的时间
        refresh.payload['exp'] = refresh.payload['iat'] - 1

        response = self.client.post('/api/users/token/refresh/', {
            'refresh': str(refresh)
        })

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        
    def test_authenticated_request_with_valid_token(self):
        """测试使用有效token的认证请求"""
        # 获取token
        refresh = RefreshToken.for_user(self.user)
        access_token = str(refresh.access_token)
        
        # 设置认证头
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        
        # 测试访问受保护端点
        response = self.client.get('/api/health-check/')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['authenticated'] is True
        assert response.data['user'] == self.user.username
        
    def test_authenticated_request_with_invalid_token(self):
        """测试使用无效token的认证请求"""
        # 设置无效的认证头
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')

        # 测试访问受保护端点
        response = self.client.get('/api/health-check/')
        # Django REST Framework可能返回401或403，取决于认证类的顺序
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
        
    def test_authenticated_request_without_token(self):
        """测试没有token的认证请求"""
        # 不设置认证头
        response = self.client.get('/api/health-check/')
        # Django REST Framework可能返回401或403，取决于认证类的顺序
        assert response.status_code in [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]
        
    def test_login_response_format(self):
        """测试登录响应格式"""
        response = self.client.post('/api/users/login/', self.test_user_data)
        
        assert response.status_code == status.HTTP_200_OK
        
        # 验证响应包含必要字段
        required_fields = ['access', 'refresh']
        for field in required_fields:
            assert field in response.data, f"Missing field: {field}"
            
        # 验证token不为空
        assert response.data['access'], "Access token is empty"
        assert response.data['refresh'], "Refresh token is empty"
        
    def test_token_rotation_on_refresh(self):
        """测试token刷新时的轮换机制"""
        # 获取初始refresh token
        refresh = RefreshToken.for_user(self.user)
        initial_refresh_token = str(refresh)

        # 刷新token
        response = self.client.post('/api/users/token/refresh/', {
            'refresh': initial_refresh_token
        })

        assert response.status_code == status.HTTP_200_OK

        # 检查是否返回了新的refresh token（如果启用了轮换）
        if 'refresh' in response.data:
            new_refresh_token = response.data['refresh']
            assert new_refresh_token != initial_refresh_token, "Refresh token should be rotated"

            # 验证旧的refresh token不能再使用（如果启用了轮换）
            old_response = self.client.post('/api/users/token/refresh/', {
                'refresh': initial_refresh_token
            })
            # 注意：根据Django设置，这可能成功或失败
            # 如果ROTATE_REFRESH_TOKENS=True，应该失败
            # 如果ROTATE_REFRESH_TOKENS=False，可能成功
            assert old_response.status_code in [status.HTTP_200_OK, status.HTTP_401_UNAUTHORIZED]
            
    def test_user_subscription_level_in_token(self):
        """测试token中包含用户订阅级别"""
        # 设置用户订阅级别
        self.user.subscription_level = 'Pro'
        self.user.save()
        
        # 登录获取token
        response = self.client.post('/api/users/login/', self.test_user_data)
        assert response.status_code == status.HTTP_200_OK
        
        # 解码token验证订阅级别
        import jwt
        from django.conf import settings
        
        access_token = response.data['access']
        decoded = jwt.decode(access_token, options={"verify_signature": False})
        
        assert 'subscription_level' in decoded
        assert decoded['subscription_level'] == 'Pro'
        assert decoded['username'] == self.user.username
        
    def test_concurrent_token_refresh(self):
        """测试并发token刷新"""
        refresh = RefreshToken.for_user(self.user)
        refresh_token = str(refresh)
        
        # 模拟并发刷新请求
        responses = []
        for _ in range(3):
            response = self.client.post('/api/users/token/refresh/', {
                'refresh': refresh_token
            })
            responses.append(response)
            
        # 第一个请求应该成功
        assert responses[0].status_code == status.HTTP_200_OK
        
        # 如果启用了token轮换，后续请求可能失败
        # 这取决于Django的ROTATE_REFRESH_TOKENS设置


class AuthenticationIntegrationTest(TestCase):
    """认证系统集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.client = APIClient()
        self.test_user_data = {
            'username': 'integrationuser',
            'password': 'testpass123'
        }
        self.user = User.objects.create_user(**self.test_user_data)
        
    def test_full_authentication_flow(self):
        """测试完整的认证流程"""
        # 1. 登录
        login_response = self.client.post('/api/users/login/', self.test_user_data)
        assert login_response.status_code == status.HTTP_200_OK
        
        access_token = login_response.data['access']
        refresh_token = login_response.data['refresh']
        
        # 2. 使用access token访问受保护资源
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        health_response = self.client.get('/api/health-check/')
        assert health_response.status_code == status.HTTP_200_OK
        
        # 3. 刷新token
        refresh_response = self.client.post('/api/users/token/refresh/', {
            'refresh': refresh_token
        })
        assert refresh_response.status_code == status.HTTP_200_OK
        
        new_access_token = refresh_response.data['access']
        
        # 4. 使用新token访问受保护资源
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {new_access_token}')
        health_response2 = self.client.get('/api/health-check/')
        assert health_response2.status_code == status.HTTP_200_OK
        
    def test_authentication_error_handling(self):
        """测试认证错误处理"""
        # 测试各种错误情况
        error_cases = [
            ('', [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]),  # 空token
            ('Bearer', [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]),  # 不完整的Bearer
            ('Bearer invalid', [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]),  # 无效token
            ('InvalidFormat token', [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN]),  # 错误格式
        ]

        for auth_header, expected_statuses in error_cases:
            if auth_header:
                self.client.credentials(HTTP_AUTHORIZATION=auth_header)
            else:
                self.client.credentials()  # 清除认证头

            response = self.client.get('/api/health-check/')
            assert response.status_code in expected_statuses, f"Failed for auth_header: {auth_header}, got {response.status_code}"


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
