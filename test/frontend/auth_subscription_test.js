/**
 * 登录权限功能测试
 *
 * 测试登录流程中的权限请求和订阅功能是否正常工作
 */

const http = require('http');
const { URL } = require('url');

// 测试配置
const TEST_CONFIG = {
    API_BASE_URL: 'http://localhost:8000',
    TEST_USER: {
        username: 'test',
        password: 'test123'
    }
};

/**
 * 模拟HTTP请求 - 使用内置http模块
 */
async function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);
        const postData = options.body || '';

        const requestOptions = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || 8000,
            path: parsedUrl.pathname,
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                ...options.headers
            }
        };

        console.log(`Making request to: ${url}`);
        console.log(`Options:`, JSON.stringify(requestOptions, null, 2));

        const req = http.request(requestOptions, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    console.log(`Response status: ${res.statusCode}`);
                    console.log(`Response data:`, JSON.stringify(jsonData, null, 2));
                    resolve({
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode },
                        data: jsonData
                    });
                } catch (e) {
                    console.log(`Response status: ${res.statusCode}`);
                    console.log(`Response data (raw):`, data);
                    resolve({
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode },
                        data: { error: 'Invalid JSON response', raw: data }
                    });
                }
            });
        });

        req.on('error', (err) => {
            console.error('Request error:', err.message);
            reject(err);
        });

        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

/**
 * 测试用户登录流程
 */
async function testUserLogin() {
    console.log('\n=== 测试用户登录流程 ===');
    
    try {
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/users/login/`,
            {
                method: 'POST',
                body: JSON.stringify(TEST_CONFIG.TEST_USER)
            }
        );
        
        if (response.ok && data.access) {
            console.log('✅ 登录成功');
            console.log(`Token: ${data.access.substring(0, 20)}...`);
            return data.access;
        } else {
            console.log('❌ 登录失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 登录请求失败:', error.message);
        return null;
    }
}

/**
 * 测试订阅信息获取
 */
async function testSubscriptionInfo(token) {
    console.log('\n=== 测试订阅信息获取 ===');
    
    if (!token) {
        console.log('❌ 没有有效的token，跳过订阅信息测试');
        return false;
    }
    
    try {
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/subscription/`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );
        
        if (response.ok) {
            console.log('✅ 订阅信息获取成功');
            console.log(`当前订阅等级: ${data.current_level}`);
            console.log(`用户名: ${data.username}`);
            return true;
        } else {
            console.log('❌ 订阅信息获取失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 订阅信息请求失败:', error.message);
        return false;
    }
}

/**
 * 测试订阅等级更新（调试模式）
 */
async function testSubscriptionUpdate(token) {
    console.log('\n=== 测试订阅等级更新（调试模式）===');
    
    if (!token) {
        console.log('❌ 没有有效的token，跳过订阅更新测试');
        return false;
    }
    
    try {
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/subscription/`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'X-Debug-Mode': 'true'
                },
                body: JSON.stringify({
                    target_level: 'Pro',
                    is_debug: true
                })
            }
        );
        
        if (response.ok && data.new_level) {
            console.log('✅ 订阅等级更新成功');
            console.log(`新等级: ${data.new_level}`);
            console.log(`新Token: ${data.access_token ? data.access_token.substring(0, 20) + '...' : '无'}`);
            return data.access_token || token;
        } else {
            console.log('❌ 订阅等级更新失败');
            return false;
        }
    } catch (error) {
        console.error('❌ 订阅更新请求失败:', error.message);
        return false;
    }
}

/**
 * 测试权限验证
 */
async function testPermissionValidation(token) {
    console.log('\n=== 测试权限验证 ===');

    if (!token) {
        console.log('❌ 没有有效的token，跳过权限验证测试');
        return false;
    }

    // 首先获取可用的模板列表
    try {
        const { response: templatesResponse, data: templatesData } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/templates/`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );

        // 检查模板数据结构
        let templates = [];
        if (templatesData && Array.isArray(templatesData)) {
            templates = templatesData;
        } else if (templatesData && templatesData.results && Array.isArray(templatesData.results)) {
            templates = templatesData.results;
        } else if (templatesData && templatesData.data && Array.isArray(templatesData.data)) {
            templates = templatesData.data;
        }

        if (!templatesResponse.ok || !templates || templates.length === 0) {
            console.log('⚠️ 没有可用的模板，跳过房间创建测试');
            console.log('✅ 权限验证通过（Token有效）');
            return true;
        }

        // 找到第一个有效的模板进行测试
        let template = null;
        for (const t of templates) {
            if (t && (t.id || t.event_template_id)) {
                template = t;
                break;
            }
        }

        if (!template) {
            console.log('⚠️ 没有有效的模板，跳过房间创建测试');
            console.log('✅ 权限验证通过（Token有效）');
            return true;
        }

        const templateId = template.id || template.event_template_id;
        const templateName = template.name || `Template ${templateId}`;
        console.log(`使用模板: ${templateName} (ID: ${templateId})`);

        // 测试创建房间（需要认证）
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/rooms/`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    template_type: 'system',
                    template_id: templateId,
                    duration_hours: 2
                })
            }
        );

        if (response.status === 201) {
            console.log('✅ 房间创建权限验证通过');
            return true;
        } else if (response.status === 403) {
            console.log('⚠️ 房间创建被拒绝（可能是订阅限制）');
            return true; // 这也是正常的权限验证
        } else if (response.status === 404) {
            console.log('⚠️ 模板不存在，但权限验证通过');
            return true; // Token有效，只是模板问题
        } else {
            console.log(`❌ 权限验证异常，状态码: ${response.status}`);
            console.log(`响应数据:`, data);
            return false;
        }
    } catch (error) {
        console.error('❌ 权限验证请求失败:', error.message);
        return false;
    }
}

/**
 * 主测试函数
 */
async function runAuthSubscriptionTests() {
    console.log('开始登录权限功能测试...\n');
    
    let allTestsPassed = true;
    
    // 1. 测试登录
    const token = await testUserLogin();
    if (!token) {
        allTestsPassed = false;
    }
    
    // 2. 测试订阅信息获取
    const subscriptionInfoSuccess = await testSubscriptionInfo(token);
    if (!subscriptionInfoSuccess) {
        allTestsPassed = false;
    }
    
    // 3. 测试订阅等级更新
    const newToken = await testSubscriptionUpdate(token);
    if (!newToken) {
        allTestsPassed = false;
    }
    
    // 4. 测试权限验证
    const permissionSuccess = await testPermissionValidation(newToken || token);
    if (!permissionSuccess) {
        allTestsPassed = false;
    }
    
    // 输出测试结果
    console.log('\n=== 测试结果汇总 ===');
    if (allTestsPassed) {
        console.log('✅ 所有登录权限功能测试通过');
        process.exit(0);
    } else {
        console.log('❌ 部分登录权限功能测试失败');
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runAuthSubscriptionTests().catch(error => {
        console.error('测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = {
    runAuthSubscriptionTests,
    testUserLogin,
    testSubscriptionInfo,
    testSubscriptionUpdate,
    testPermissionValidation
};
