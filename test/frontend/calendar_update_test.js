/**
 * 日历更新功能测试
 * 
 * 测试预约创建、修改、取消后日历数据是否正确更新
 */

const http = require('http');
const { URL } = require('url');

// 测试配置
const TEST_CONFIG = {
    API_BASE_URL: 'http://localhost:8000',
    TEST_USER: {
        username: 'test',
        password: 'test123'
    }
};

/**
 * 模拟HTTP请求 - 使用内置http模块
 */
async function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);
        const postData = options.body || '';
        
        const requestOptions = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || 8000,
            path: parsedUrl.pathname + (parsedUrl.search || ''),
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                ...options.headers
            }
        };
        
        console.log(`Making request to: ${url}`);
        console.log(`Method: ${requestOptions.method}`);
        
        const req = http.request(requestOptions, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    console.log(`Response status: ${res.statusCode}`);
                    resolve({ 
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode }, 
                        data: jsonData 
                    });
                } catch (e) {
                    console.log(`Response status: ${res.statusCode}`);
                    console.log(`Response data (raw):`, data);
                    resolve({ 
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode }, 
                        data: { error: 'Invalid JSON response', raw: data } 
                    });
                }
            });
        });
        
        req.on('error', (err) => {
            console.error('Request error:', err.message);
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

/**
 * 用户登录
 */
async function loginUser() {
    try {
        const { response, data } = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/users/login/`, {
            method: 'POST',
            body: JSON.stringify(TEST_CONFIG.TEST_USER)
        });

        if (response.ok && data.access) {
            console.log('✅ 登录成功');
            return data.access;
        } else {
            console.log('❌ 登录失败:', data);
            return null;
        }
    } catch (error) {
        console.log('❌ 登录请求失败:', error.message);
        return null;
    }
}

/**
 * 获取日历数据
 */
async function getCalendarData(token, year, month) {
    try {
        // 构建正确的查询参数
        const monthStr = `${year}-${String(month).padStart(2, '0')}`;
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/calendar/?month=${monthStr}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );

        if (response.ok) {
            console.log(`✅ 获取${year}年${month}月日历数据成功`);
            return data;
        } else {
            console.log(`❌ 获取日历数据失败:`, data);
            return null;
        }
    } catch (error) {
        console.log('❌ 获取日历数据请求失败:', error.message);
        return null;
    }
}

/**
 * 创建预约
 */
async function createReservation(token) {
    try {
        // 创建一个未来的预约时间（明天同一时间）
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(14, 0, 0, 0); // 设置为下午2点
        
        const reservationData = {
            name: '测试预约房间',
            template_id: 'system_1', // 使用系统模板
            scheduled_start_time: tomorrow.toISOString(),
            duration_hours: 2
        };

        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/rooms/schedule/`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(reservationData)
            }
        );

        if (response.ok) {
            console.log('✅ 创建预约成功');
            console.log(`预约ID: ${data.id}, 房间代码: ${data.room_code}`);
            return data;
        } else {
            console.log('❌ 创建预约失败:', data);
            return null;
        }
    } catch (error) {
        console.log('❌ 创建预约请求失败:', error.message);
        return null;
    }
}

/**
 * 修改预约
 */
async function updateReservation(token, reservationId) {
    try {
        // 修改预约时间（推迟1小时）
        const newTime = new Date();
        newTime.setDate(newTime.getDate() + 1);
        newTime.setHours(15, 0, 0, 0); // 设置为下午3点
        
        const updateData = {
            name: '修改后的测试预约',
            scheduled_start_time: newTime.toISOString(),
            duration_hours: 3
        };

        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/calendar/reservations/${reservationId}/`,
            {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(updateData)
            }
        );

        if (response.ok) {
            console.log('✅ 修改预约成功');
            return data;
        } else {
            console.log('❌ 修改预约失败:', data);
            return null;
        }
    } catch (error) {
        console.log('❌ 修改预约请求失败:', error.message);
        return null;
    }
}

/**
 * 取消预约
 */
async function cancelReservation(token, reservationId) {
    try {
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/calendar/reservations/${reservationId}/`,
            {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );

        if (response.ok) {
            console.log('✅ 取消预约成功');
            return true;
        } else {
            console.log('❌ 取消预约失败:', data);
            return false;
        }
    } catch (error) {
        console.log('❌ 取消预约请求失败:', error.message);
        return false;
    }
}

/**
 * 主测试函数
 */
async function runCalendarUpdateTest() {
    console.log('开始日历更新功能测试...\n');

    // 1. 登录
    console.log('=== 测试用户登录 ===');
    const token = await loginUser();
    if (!token) {
        console.log('❌ 登录失败，终止测试');
        return;
    }

    // 2. 获取当前日历数据
    console.log('\n=== 获取初始日历数据 ===');
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    
    const initialCalendarData = await getCalendarData(token, year, month);
    if (!initialCalendarData) {
        console.log('❌ 获取初始日历数据失败，终止测试');
        return;
    }
    
    const initialReservationCount = initialCalendarData.reservations ? initialCalendarData.reservations.length : 0;
    console.log(`初始预约数量: ${initialReservationCount}`);

    // 3. 创建预约
    console.log('\n=== 测试创建预约 ===');
    const newReservation = await createReservation(token);
    if (!newReservation) {
        console.log('❌ 创建预约失败，终止测试');
        return;
    }

    // 4. 等待后端更新，然后获取更新后的日历数据
    console.log('\n=== 验证预约创建后日历更新 ===');
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    
    const afterCreateCalendarData = await getCalendarData(token, year, month);
    if (afterCreateCalendarData) {
        const afterCreateCount = afterCreateCalendarData.reservations ? afterCreateCalendarData.reservations.length : 0;
        if (afterCreateCount > initialReservationCount) {
            console.log('✅ 创建预约后日历数据正确更新');
            console.log(`预约数量从 ${initialReservationCount} 增加到 ${afterCreateCount}`);
        } else {
            console.log('❌ 创建预约后日历数据未更新');
        }
    }

    // 5. 修改预约
    console.log('\n=== 测试修改预约 ===');
    const updatedReservation = await updateReservation(token, newReservation.id);
    if (updatedReservation) {
        // 验证修改后的数据
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
        const afterUpdateCalendarData = await getCalendarData(token, year, month);
        if (afterUpdateCalendarData) {
            console.log('✅ 修改预约后日历数据获取成功');
        }
    }

    // 6. 取消预约
    console.log('\n=== 测试取消预约 ===');
    const cancelSuccess = await cancelReservation(token, newReservation.id);
    if (cancelSuccess) {
        // 验证取消后的数据
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
        const afterCancelCalendarData = await getCalendarData(token, year, month);
        if (afterCancelCalendarData) {
            const afterCancelCount = afterCancelCalendarData.reservations ? afterCancelCalendarData.reservations.length : 0;
            if (afterCancelCount === initialReservationCount) {
                console.log('✅ 取消预约后日历数据正确更新');
                console.log(`预约数量恢复到初始值: ${afterCancelCount}`);
            } else {
                console.log('❌ 取消预约后日历数据未正确更新');
                console.log(`预约数量: ${afterCancelCount}, 期望: ${initialReservationCount}`);
            }
        }
    }

    console.log('\n=== 测试结果汇总 ===');
    console.log('✅ 日历更新功能测试完成');
}

// 运行测试
runCalendarUpdateTest().catch(console.error);
