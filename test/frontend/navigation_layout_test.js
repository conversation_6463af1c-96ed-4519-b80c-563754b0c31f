/**
 * 导航栏布局优化测试
 * 
 * 测试主页面导航栏布局是否为移动系统显示预留了足够空间
 * 验证不同设备类型的适配效果和响应式布局
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

// 测试配置
const TEST_CONFIG = {
    username: 'test_nav_user',
    password: 'testpass123',
    email: '<EMAIL>'
};

let authToken = null;

/**
 * 模拟不同设备类型的屏幕尺寸
 */
const DEVICE_TYPES = {
    SMALL_PHONE: { width: 320, height: 568, name: '小屏手机 (iPhone SE)' },
    PHONE: { width: 375, height: 667, name: '标准手机 (iPhone 8)' },
    LARGE_PHONE: { width: 414, height: 896, name: '大屏手机 (iPhone 11)' },
    TABLET: { width: 768, height: 1024, name: '平板 (iPad)' }
};

/**
 * 模拟不同平台的状态栏高度
 */
const PLATFORM_STATUS_BAR = {
    IOS_NORMAL: { height: 20, hasNotch: false, name: 'iOS 普通屏幕' },
    IOS_NOTCH: { height: 44, hasNotch: true, name: 'iOS 刘海屏' },
    ANDROID_NORMAL: { height: 24, hasNotch: false, name: 'Android 普通屏幕' },
    ANDROID_NOTCH: { height: 32, hasNotch: true, name: 'Android 异形屏' }
};

/**
 * 用户设置
 */
async function setupUser() {
    try {
        // 尝试注册用户
        try {
            await axios.post(`${BASE_URL}/api/users/register/`, {
                username: TEST_CONFIG.username,
                password: TEST_CONFIG.password,
                email: TEST_CONFIG.email
            });
            console.log('✅ 用户注册成功');
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('ℹ️ 用户已存在，跳过注册');
            } else {
                throw error;
            }
        }

        // 登录获取token
        const loginResponse = await axios.post(`${BASE_URL}/api/users/login/`, {
            username: TEST_CONFIG.username,
            password: TEST_CONFIG.password
        });

        authToken = loginResponse.data.access;
        console.log('✅ 用户登录成功');
        
        return true;
    } catch (error) {
        console.error('❌ 用户设置失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 计算设备适配参数
 */
function calculateDeviceAdaptation(deviceType, platformInfo) {
    const { width, height } = deviceType;
    const { height: statusBarHeight, hasNotch } = platformInfo;
    
    // 设备类型判断
    let deviceCategory;
    if (width >= 768) {
        deviceCategory = 'TABLET';
    } else if (width < 350) {
        deviceCategory = 'SMALL_PHONE';
    } else if (width > 414 || height / width > 2.1) {
        deviceCategory = 'LARGE_PHONE';
    } else {
        deviceCategory = 'PHONE';
    }
    
    // 安全区域计算
    let safeAreaTop = statusBarHeight;
    let safeAreaBottom = 0;
    
    if (hasNotch) {
        safeAreaTop += 10;
        if (platformInfo.name.includes('iOS')) {
            safeAreaBottom = 34;
        } else if (deviceCategory === 'LARGE_PHONE') {
            safeAreaBottom = 20;
        }
    } else {
        safeAreaTop += platformInfo.name.includes('iOS') ? 10 : 5;
    }
    
    // 导航栏高度计算
    const baseTopNavHeight = 60;
    const baseBottomNavHeight = 80;
    
    let topNavHeight, bottomNavHeight;
    switch (deviceCategory) {
        case 'SMALL_PHONE':
            topNavHeight = baseTopNavHeight * 0.9;
            bottomNavHeight = baseBottomNavHeight * 0.9;
            break;
        case 'LARGE_PHONE':
            topNavHeight = baseTopNavHeight * 1.1;
            bottomNavHeight = baseBottomNavHeight * 1.1;
            break;
        case 'TABLET':
            topNavHeight = baseTopNavHeight * 1.2;
            bottomNavHeight = baseBottomNavHeight * 1.2;
            break;
        default:
            topNavHeight = baseTopNavHeight;
            bottomNavHeight = baseBottomNavHeight;
    }
    
    bottomNavHeight += safeAreaBottom;
    
    // 内容区域计算
    const totalNavHeight = topNavHeight + bottomNavHeight + safeAreaTop;
    const contentHeight = height - totalNavHeight;
    const contentHeightRatio = contentHeight / height;
    
    return {
        deviceCategory,
        safeAreaTop,
        safeAreaBottom,
        topNavHeight,
        bottomNavHeight,
        contentHeight,
        contentHeightRatio,
        totalNavHeight
    };
}

/**
 * 测试设备适配效果
 */
async function testDeviceAdaptation() {
    console.log('\n=== 测试设备适配效果 ===');
    
    const results = [];
    
    for (const [deviceKey, deviceType] of Object.entries(DEVICE_TYPES)) {
        for (const [platformKey, platformInfo] of Object.entries(PLATFORM_STATUS_BAR)) {
            const adaptation = calculateDeviceAdaptation(deviceType, platformInfo);
            
            console.log(`\n📱 ${deviceType.name} - ${platformInfo.name}:`);
            console.log(`  - 设备分类: ${adaptation.deviceCategory}`);
            console.log(`  - 屏幕尺寸: ${deviceType.width} x ${deviceType.height}`);
            console.log(`  - 安全区域顶部: ${adaptation.safeAreaTop}px`);
            console.log(`  - 安全区域底部: ${adaptation.safeAreaBottom}px`);
            console.log(`  - 上导航栏高度: ${adaptation.topNavHeight}px`);
            console.log(`  - 下导航栏高度: ${adaptation.bottomNavHeight}px`);
            console.log(`  - 内容区域高度: ${adaptation.contentHeight}px`);
            console.log(`  - 内容区域占比: ${(adaptation.contentHeightRatio * 100).toFixed(1)}%`);
            
            // 评估适配质量
            let quality = '优秀';
            if (adaptation.contentHeightRatio < 0.6) {
                quality = '需要优化';
            } else if (adaptation.contentHeightRatio < 0.7) {
                quality = '良好';
            }
            
            console.log(`  - 适配质量: ${quality}`);
            
            results.push({
                device: deviceType.name,
                platform: platformInfo.name,
                adaptation,
                quality
            });
        }
    }
    
    return results;
}

/**
 * 测试响应式字体和间距
 */
async function testResponsiveDesign() {
    console.log('\n=== 测试响应式设计 ===');
    
    const baseFontSize = 16;
    const baseSpacing = 16;
    
    for (const [deviceKey, deviceType] of Object.entries(DEVICE_TYPES)) {
        const { width } = deviceType;
        
        // 设备类型判断
        let deviceCategory;
        if (width >= 768) {
            deviceCategory = 'TABLET';
        } else if (width < 350) {
            deviceCategory = 'SMALL_PHONE';
        } else if (width > 414) {
            deviceCategory = 'LARGE_PHONE';
        } else {
            deviceCategory = 'PHONE';
        }
        
        // 响应式计算
        let fontMultiplier, spacingMultiplier;
        switch (deviceCategory) {
            case 'SMALL_PHONE':
                fontMultiplier = 0.9;
                spacingMultiplier = 0.8;
                break;
            case 'LARGE_PHONE':
                fontMultiplier = 1.1;
                spacingMultiplier = 1.1;
                break;
            case 'TABLET':
                fontMultiplier = 1.3;
                spacingMultiplier = 1.5;
                break;
            default:
                fontMultiplier = 1.0;
                spacingMultiplier = 1.0;
        }
        
        const adaptedFontSize = baseFontSize * fontMultiplier;
        const adaptedSpacing = baseSpacing * spacingMultiplier;
        
        console.log(`\n📐 ${deviceType.name}:`);
        console.log(`  - 设备分类: ${deviceCategory}`);
        console.log(`  - 基础字体: ${baseFontSize}px → ${adaptedFontSize.toFixed(1)}px`);
        console.log(`  - 基础间距: ${baseSpacing}px → ${adaptedSpacing.toFixed(1)}px`);
        console.log(`  - 字体缩放比例: ${fontMultiplier}`);
        console.log(`  - 间距缩放比例: ${spacingMultiplier}`);
    }
}

/**
 * 测试导航栏可用性
 */
async function testNavigationUsability() {
    console.log('\n=== 测试导航栏可用性 ===');
    
    // 模拟导航栏按钮点击区域测试
    const minTouchTarget = 44; // iOS人机界面指南推荐的最小触摸目标
    
    for (const [deviceKey, deviceType] of Object.entries(DEVICE_TYPES)) {
        const { width } = deviceType;
        
        // 计算导航按钮尺寸
        let buttonSize = 28; // 基础图标尺寸
        if (width >= 768) {
            buttonSize *= 1.3;
        } else if (width < 350) {
            buttonSize *= 0.9;
        } else if (width > 414) {
            buttonSize *= 1.1;
        }
        
        // 计算触摸区域
        const touchPadding = Math.max(0, (minTouchTarget - buttonSize) / 2);
        const totalTouchArea = buttonSize + (touchPadding * 2);
        
        console.log(`\n👆 ${deviceType.name}:`);
        console.log(`  - 图标尺寸: ${buttonSize.toFixed(1)}px`);
        console.log(`  - 触摸填充: ${touchPadding.toFixed(1)}px`);
        console.log(`  - 总触摸区域: ${totalTouchArea.toFixed(1)}px`);
        console.log(`  - 可用性评级: ${totalTouchArea >= minTouchTarget ? '优秀' : '需要改进'}`);
    }
}

/**
 * 主测试函数
 */
async function runTest() {
    console.log('开始导航栏布局优化测试...\n');

    try {
        // 设置用户
        if (!await setupUser()) {
            throw new Error('用户设置失败');
        }

        // 测试设备适配效果
        const adaptationResults = await testDeviceAdaptation();

        // 测试响应式设计
        await testResponsiveDesign();

        // 测试导航栏可用性
        await testNavigationUsability();

        console.log('\n=== 测试结果汇总 ===');
        console.log('✅ 导航栏布局优化测试完成');
        
        // 统计适配质量
        const qualityStats = adaptationResults.reduce((stats, result) => {
            stats[result.quality] = (stats[result.quality] || 0) + 1;
            return stats;
        }, {});
        
        console.log('📊 适配质量统计:');
        Object.entries(qualityStats).forEach(([quality, count]) => {
            console.log(`  - ${quality}: ${count}个配置`);
        });
        
        // 检查是否有需要优化的配置
        const needsOptimization = adaptationResults.filter(r => r.quality === '需要优化').length;
        console.log(`🎯 整体评估: ${needsOptimization === 0 ? '所有设备适配良好' : `${needsOptimization}个配置需要优化`}`);

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = {
    runTest,
    setupUser,
    testDeviceAdaptation,
    testResponsiveDesign,
    testNavigationUsability,
    calculateDeviceAdaptation
};
