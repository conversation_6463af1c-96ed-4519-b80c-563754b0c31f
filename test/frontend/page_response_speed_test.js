/**
 * 页面响应速度优化测试
 * 
 * 测试页面切换延迟和加载竞争问题的修复
 * 验证页面预加载和缓存机制的性能提升
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

// 测试配置
const TEST_CONFIG = {
    username: 'test_speed_user',
    password: 'testpass123',
    email: '<EMAIL>'
};

let authToken = null;

/**
 * 用户设置
 */
async function setupUser() {
    try {
        // 尝试注册用户
        try {
            await axios.post(`${BASE_URL}/api/users/register/`, {
                username: TEST_CONFIG.username,
                password: TEST_CONFIG.password,
                email: TEST_CONFIG.email
            });
            console.log('✅ 用户注册成功');
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('ℹ️ 用户已存在，跳过注册');
            } else {
                throw error;
            }
        }

        // 登录获取token
        const loginResponse = await axios.post(`${BASE_URL}/api/users/login/`, {
            username: TEST_CONFIG.username,
            password: TEST_CONFIG.password
        });

        authToken = loginResponse.data.access;
        console.log('✅ 用户登录成功');
        
        return true;
    } catch (error) {
        console.error('❌ 用户设置失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 测试API响应时间
 */
async function testApiResponseTimes() {
    console.log('\n=== 测试API响应时间 ===');
    
    const apiEndpoints = [
        { name: '用户信息', url: '/api/users/me/', method: 'GET' },
        { name: '订阅信息', url: '/api/subscription/', method: 'GET' },
        { name: '模板列表', url: '/api/templates/', method: 'GET' },
        { name: '房间列表', url: '/api/rooms/', method: 'GET' },
        { name: '日历数据', url: '/api/calendar/', method: 'GET' }
    ];

    const results = [];

    for (const endpoint of apiEndpoints) {
        let startTime;
        try {
            startTime = Date.now();

            const response = await axios({
                method: endpoint.method,
                url: `${BASE_URL}${endpoint.url}`,
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });

            const endTime = Date.now();
            const responseTime = endTime - startTime;

            results.push({
                name: endpoint.name,
                responseTime,
                status: response.status,
                success: true
            });

            console.log(`📊 ${endpoint.name}: ${responseTime}ms (${response.status})`);

        } catch (error) {
            const endTime = Date.now();
            const responseTime = startTime ? endTime - startTime : 0;

            results.push({
                name: endpoint.name,
                responseTime,
                status: error.response?.status || 'ERROR',
                success: false,
                error: error.message
            });

            console.log(`❌ ${endpoint.name}: ${responseTime}ms (${error.response?.status || 'ERROR'})`);
        }
    }

    // 分析结果
    const successfulRequests = results.filter(r => r.success);
    const averageResponseTime = successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length;
    const maxResponseTime = Math.max(...successfulRequests.map(r => r.responseTime));
    const minResponseTime = Math.min(...successfulRequests.map(r => r.responseTime));

    console.log('\n📈 API响应时间分析:');
    console.log(`  - 平均响应时间: ${averageResponseTime.toFixed(2)}ms`);
    console.log(`  - 最快响应时间: ${minResponseTime}ms`);
    console.log(`  - 最慢响应时间: ${maxResponseTime}ms`);
    console.log(`  - 成功请求数: ${successfulRequests.length}/${results.length}`);

    return {
        results,
        averageResponseTime,
        maxResponseTime,
        minResponseTime,
        successRate: successfulRequests.length / results.length
    };
}

/**
 * 测试并发请求性能
 */
async function testConcurrentRequests() {
    console.log('\n=== 测试并发请求性能 ===');
    
    const concurrentRequests = [
        () => axios.get(`${BASE_URL}/api/users/me/`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
        () => axios.get(`${BASE_URL}/api/subscription/`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
        () => axios.get(`${BASE_URL}/api/templates/`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
        () => axios.get(`${BASE_URL}/api/rooms/`, { headers: { 'Authorization': `Bearer ${authToken}` } }),
        () => axios.get(`${BASE_URL}/api/calendar/`, { headers: { 'Authorization': `Bearer ${authToken}` } })
    ];

    try {
        const startTime = Date.now();
        
        // 并发执行所有请求
        const results = await Promise.allSettled(concurrentRequests.map(request => request()));
        
        const endTime = Date.now();
        const totalTime = endTime - startTime;
        
        const successCount = results.filter(r => r.status === 'fulfilled').length;
        const failureCount = results.filter(r => r.status === 'rejected').length;
        
        console.log(`⚡ 并发请求完成时间: ${totalTime}ms`);
        console.log(`✅ 成功请求: ${successCount}`);
        console.log(`❌ 失败请求: ${failureCount}`);
        
        // 分析失败原因
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                console.log(`   请求${index + 1}失败: ${result.reason.message}`);
            }
        });

        return {
            totalTime,
            successCount,
            failureCount,
            successRate: successCount / results.length
        };
        
    } catch (error) {
        console.error('❌ 并发请求测试失败:', error.message);
        return null;
    }
}

/**
 * 测试页面加载竞争场景
 */
async function testPageLoadingCompetition() {
    console.log('\n=== 测试页面加载竞争场景 ===');
    
    // 模拟快速切换页面的场景
    const pageLoadSequence = [
        { name: '房间页面', delay: 0 },
        { name: '环节编辑器', delay: 100 },
        { name: '日历页面', delay: 200 },
        { name: '房间页面', delay: 300 },
        { name: '环节编辑器', delay: 400 }
    ];

    const loadResults = [];

    for (const page of pageLoadSequence) {
        let startTime;
        try {
            await new Promise(resolve => setTimeout(resolve, page.delay));

            startTime = Date.now();

            // 模拟页面数据加载
            let apiCall;
            switch (page.name) {
                case '房间页面':
                    apiCall = axios.get(`${BASE_URL}/api/rooms/`, { headers: { 'Authorization': `Bearer ${authToken}` } });
                    break;
                case '环节编辑器':
                    apiCall = axios.get(`${BASE_URL}/api/templates/`, { headers: { 'Authorization': `Bearer ${authToken}` } });
                    break;
                case '日历页面':
                    apiCall = axios.get(`${BASE_URL}/api/calendar/`, { headers: { 'Authorization': `Bearer ${authToken}` } });
                    break;
                default:
                    apiCall = Promise.resolve({ status: 200 });
            }

            await apiCall;
            const endTime = Date.now();
            const loadTime = endTime - startTime;

            loadResults.push({
                page: page.name,
                loadTime,
                success: true
            });

            console.log(`📄 ${page.name}: ${loadTime}ms`);

        } catch (error) {
            const endTime = Date.now();
            const loadTime = startTime ? endTime - startTime : 0;

            loadResults.push({
                page: page.name,
                loadTime,
                success: false,
                error: error.message
            });

            console.log(`❌ ${page.name}: ${loadTime}ms (失败)`);
        }
    }

    const averageLoadTime = loadResults.reduce((sum, r) => sum + r.loadTime, 0) / loadResults.length;
    const successfulLoads = loadResults.filter(r => r.success).length;

    console.log('\n📊 页面加载竞争分析:');
    console.log(`  - 平均加载时间: ${averageLoadTime.toFixed(2)}ms`);
    console.log(`  - 成功加载页面: ${successfulLoads}/${loadResults.length}`);
    console.log(`  - 是否存在加载冲突: ${successfulLoads < loadResults.length ? '是' : '否'}`);

    return {
        loadResults,
        averageLoadTime,
        successRate: successfulLoads / loadResults.length
    };
}

/**
 * 主测试函数
 */
async function runTest() {
    console.log('开始页面响应速度优化测试...\n');

    try {
        // 设置用户
        if (!await setupUser()) {
            throw new Error('用户设置失败');
        }

        // 测试API响应时间
        const apiResults = await testApiResponseTimes();

        // 测试并发请求性能
        const concurrentResults = await testConcurrentRequests();

        // 测试页面加载竞争
        const competitionResults = await testPageLoadingCompetition();

        console.log('\n=== 测试结果汇总 ===');
        console.log('✅ 页面响应速度优化测试完成');
        console.log(`📊 API平均响应时间: ${apiResults.averageResponseTime.toFixed(2)}ms`);
        console.log(`⚡ 并发请求完成时间: ${concurrentResults?.totalTime || 'N/A'}ms`);
        console.log(`📄 页面平均加载时间: ${competitionResults.averageLoadTime.toFixed(2)}ms`);
        
        // 性能评估
        const isPerformanceGood = 
            apiResults.averageResponseTime < 500 && 
            (concurrentResults?.totalTime || 0) < 2000 && 
            competitionResults.averageLoadTime < 300;
            
        console.log(`🎯 整体性能评估: ${isPerformanceGood ? '优秀' : '需要优化'}`);

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = {
    runTest,
    setupUser,
    testApiResponseTimes,
    testConcurrentRequests,
    testPageLoadingCompetition
};
