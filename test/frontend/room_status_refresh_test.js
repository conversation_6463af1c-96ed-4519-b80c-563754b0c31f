/**
 * 房间状态刷新功能测试
 * 
 * 测试房间状态在环节转换时是否正确更新前端显示
 * 验证WebSocket消息处理和状态同步机制
 */

const axios = require('axios');
const WebSocket = require('ws');

const BASE_URL = 'http://localhost:8000';
const WS_BASE_URL = 'ws://localhost:8000';

// 测试配置
const TEST_CONFIG = {
    username: 'test_host_status',
    password: 'testpass123',
    email: '<EMAIL>'
};

let authToken = null;
let roomCode = null;
let ws = null;

/**
 * 用户注册和登录
 */
async function setupUser() {
    try {
        // 尝试注册用户
        try {
            await axios.post(`${BASE_URL}/api/users/register/`, {
                username: TEST_CONFIG.username,
                password: TEST_CONFIG.password,
                email: TEST_CONFIG.email
            });
            console.log('✅ 用户注册成功');
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('ℹ️ 用户已存在，跳过注册');
            } else {
                throw error;
            }
        }

        // 登录获取token
        const loginResponse = await axios.post(`${BASE_URL}/api/users/login/`, {
            username: TEST_CONFIG.username,
            password: TEST_CONFIG.password
        });

        authToken = loginResponse.data.access;
        console.log('✅ 用户登录成功');
        return true;
    } catch (error) {
        console.error('❌ 用户设置失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 创建测试房间
 */
async function createTestRoom() {
    try {
        const response = await axios.post(`${BASE_URL}/api/rooms/`, {
            template_id: 'system_1' // 使用系统默认模板
        }, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        roomCode = response.data.room_code;
        console.log(`✅ 创建房间成功: ${roomCode}`);
        return true;
    } catch (error) {
        console.error('❌ 创建房间失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 建立WebSocket连接
 */
async function connectWebSocket() {
    return new Promise((resolve, reject) => {
        const wsUrl = `${WS_BASE_URL}/ws/room/${roomCode}/?token=${authToken}`;
        ws = new WebSocket(wsUrl);

        ws.on('open', () => {
            console.log('✅ WebSocket连接成功');
            resolve(true);
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket连接失败:', error);
            reject(error);
        });

        ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                console.log(`📨 收到消息: ${message.type}`, message.payload);
                
                // 处理房间状态更新消息
                if (message.type === 'room_condition_update') {
                    console.log(`🔄 房间状态更新: ${message.payload.room_status}`);
                    console.log(`📋 下一环节准备状态: ${message.payload.is_ready_for_next_step}`);
                    console.log(`💬 条件消息: ${message.payload.condition_message}`);
                }
            } catch (error) {
                console.error('❌ 解析WebSocket消息失败:', error);
            }
        });

        // 设置超时
        setTimeout(() => {
            if (ws.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket连接超时'));
            }
        }, 5000);
    });
}

/**
 * 发送WebSocket消息
 */
function sendWebSocketMessage(action, payload = {}) {
    if (ws && ws.readyState === WebSocket.OPEN) {
        const message = JSON.stringify({ action, payload });
        ws.send(message);
        console.log(`📤 发送消息: ${action}`, payload);
    } else {
        console.error('❌ WebSocket未连接');
    }
}

/**
 * 等待指定时间
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试房间状态刷新功能
 */
async function testRoomStatusRefresh() {
    console.log('\n=== 测试房间状态刷新功能 ===');

    // 1. 请求初始房间状态
    console.log('\n1. 请求初始房间状态');
    sendWebSocketMessage('request_room_status');
    await delay(1000);

    // 2. 开始下一环节
    console.log('\n2. 开始下一环节');
    sendWebSocketMessage('next_step');
    await delay(2000);

    // 3. 再次请求房间状态
    console.log('\n3. 环节开始后请求房间状态');
    sendWebSocketMessage('request_room_status');
    await delay(1000);

    // 4. 回到大厅
    console.log('\n4. 回到大厅');
    sendWebSocketMessage('return_to_lobby');
    await delay(2000);

    // 5. 最终状态检查
    console.log('\n5. 回到大厅后请求房间状态');
    sendWebSocketMessage('request_room_status');
    await delay(1000);

    console.log('\n✅ 房间状态刷新测试完成');
}

/**
 * 清理资源
 */
async function cleanup() {
    if (ws) {
        ws.close();
        console.log('✅ WebSocket连接已关闭');
    }

    if (roomCode && authToken) {
        try {
            await axios.delete(`${BASE_URL}/api/rooms/${roomCode}/`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`
                }
            });
            console.log('✅ 测试房间已删除');
        } catch (error) {
            console.log('ℹ️ 房间删除失败或已不存在');
        }
    }
}

/**
 * 主测试函数
 */
async function runTest() {
    console.log('开始房间状态刷新功能测试...\n');

    try {
        // 设置用户
        if (!await setupUser()) {
            throw new Error('用户设置失败');
        }

        // 创建房间
        if (!await createTestRoom()) {
            throw new Error('房间创建失败');
        }

        // 连接WebSocket
        await connectWebSocket();

        // 等待连接稳定
        await delay(1000);

        // 执行测试
        await testRoomStatusRefresh();

        console.log('\n=== 测试结果汇总 ===');
        console.log('✅ 房间状态刷新功能测试完成');
        console.log('✅ WebSocket消息收发正常');
        console.log('✅ 状态同步机制工作正常');

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        process.exit(1);
    } finally {
        await cleanup();
    }
}

// 运行测试
if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = {
    runTest,
    setupUser,
    createTestRoom,
    connectWebSocket,
    testRoomStatusRefresh,
    cleanup
};
