/**
 * Settings页面加载功能测试
 * 
 * 测试Settings页面在处理空字段时是否能正常加载
 * 验证用户信息和订阅信息的空值处理机制
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8000';

// 测试配置
const TEST_CONFIG = {
    username: 'test_settings_user',
    password: 'testpass123',
    email: '<EMAIL>'
};

let authToken = null;

/**
 * 用户注册和登录
 */
async function setupUser() {
    try {
        // 尝试注册用户
        try {
            await axios.post(`${BASE_URL}/api/users/register/`, {
                username: TEST_CONFIG.username,
                password: TEST_CONFIG.password,
                email: TEST_CONFIG.email
            });
            console.log('✅ 用户注册成功');
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('ℹ️ 用户已存在，跳过注册');
            } else {
                throw error;
            }
        }

        // 登录获取token
        const loginResponse = await axios.post(`${BASE_URL}/api/users/login/`, {
            username: TEST_CONFIG.username,
            password: TEST_CONFIG.password
        });

        authToken = loginResponse.data.access;
        console.log('✅ 用户登录成功');
        
        // 验证token中包含的用户信息
        const tokenPayload = JSON.parse(Buffer.from(authToken.split('.')[1], 'base64').toString());
        console.log('📋 Token中的用户信息:', {
            username: tokenPayload.username,
            email: tokenPayload.email || '未设置',
            subscription_level: tokenPayload.subscription_level
        });
        
        return true;
    } catch (error) {
        console.error('❌ 用户设置失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 测试订阅信息API
 */
async function testSubscriptionInfo() {
    try {
        console.log('\n=== 测试订阅信息API ===');
        
        const response = await axios.get(`${BASE_URL}/api/subscription/`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        console.log('✅ 订阅信息获取成功:', response.data);
        
        // 验证订阅信息字段
        const subscriptionInfo = response.data;
        console.log('📋 订阅信息字段验证:');
        console.log(`  - current_level: ${subscriptionInfo.current_level || '未设置'}`);
        console.log(`  - max_participants: ${subscriptionInfo.max_participants || '未设置'}`);
        console.log(`  - max_templates: ${subscriptionInfo.max_templates || '未设置'}`);
        console.log(`  - max_steps_per_template: ${subscriptionInfo.max_steps_per_template || '未设置'}`);
        
        return true;
    } catch (error) {
        console.error('❌ 订阅信息获取失败:', error.response?.data || error.message);
        return false;
    }
}

/**
 * 测试用户信息字段处理
 */
async function testUserInfoHandling() {
    console.log('\n=== 测试用户信息字段处理 ===');
    
    // 解码JWT token获取用户信息
    try {
        const tokenPayload = JSON.parse(Buffer.from(authToken.split('.')[1], 'base64').toString());
        
        console.log('🔍 JWT Token字段检查:');
        console.log(`  - username: ${tokenPayload.username ? '✅ 存在' : '❌ 缺失'}`);
        console.log(`  - email: ${tokenPayload.email ? '✅ 存在' : '⚠️ 可选字段未设置'}`);
        console.log(`  - subscription_level: ${tokenPayload.subscription_level ? '✅ 存在' : '❌ 缺失'}`);
        console.log(`  - user_id: ${tokenPayload.user_id ? '✅ 存在' : '❌ 缺失'}`);
        console.log(`  - exp: ${tokenPayload.exp ? '✅ 存在' : '❌ 缺失'}`);
        
        // 模拟Settings页面的字段访问
        console.log('\n🎯 模拟Settings页面字段访问:');
        const username = tokenPayload.username || '用户';
        const email = tokenPayload.email || '未绑定邮箱';
        const subscriptionLevel = tokenPayload.subscription_level || 'Free';
        
        console.log(`  - 显示用户名: "${username}"`);
        console.log(`  - 显示邮箱: "${email}"`);
        console.log(`  - 显示订阅等级: "${subscriptionLevel}"`);
        console.log(`  - 头像首字母: "${username.charAt(0).toUpperCase()}"`);
        
        return true;
    } catch (error) {
        console.error('❌ JWT Token解码失败:', error);
        return false;
    }
}

/**
 * 测试空值处理场景
 */
async function testEmptyFieldHandling() {
    console.log('\n=== 测试空值处理场景 ===');
    
    // 模拟各种空值情况
    const testCases = [
        { name: '用户名为空', user: { username: '', email: '<EMAIL>', subscription_level: 'Free' } },
        { name: '邮箱为空', user: { username: 'testuser', email: '', subscription_level: 'Free' } },
        { name: '邮箱未定义', user: { username: 'testuser', subscription_level: 'Free' } },
        { name: '订阅等级为空', user: { username: 'testuser', email: '<EMAIL>', subscription_level: '' } },
        { name: '订阅等级未定义', user: { username: 'testuser', email: '<EMAIL>' } },
        { name: '所有字段为空', user: { username: '', email: '', subscription_level: '' } },
        { name: '用户对象为null', user: null },
        { name: '用户对象未定义', user: undefined }
    ];
    
    testCases.forEach((testCase, index) => {
        console.log(`\n${index + 1}. ${testCase.name}:`);
        
        try {
            const user = testCase.user;
            
            // 模拟Settings页面的安全访问模式
            const displayUsername = user?.username || '用户';
            const displayEmail = user?.email || '未绑定邮箱';
            const displaySubscriptionLevel = user?.subscription_level || 'Free';
            const avatarLetter = user?.username?.charAt(0)?.toUpperCase() || '用';
            
            console.log(`   - 用户名显示: "${displayUsername}"`);
            console.log(`   - 邮箱显示: "${displayEmail}"`);
            console.log(`   - 订阅等级显示: "${displaySubscriptionLevel}"`);
            console.log(`   - 头像字母: "${avatarLetter}"`);
            console.log(`   ✅ 处理成功，无错误`);
            
        } catch (error) {
            console.log(`   ❌ 处理失败: ${error.message}`);
        }
    });
    
    return true;
}

/**
 * 主测试函数
 */
async function runTest() {
    console.log('开始Settings页面加载功能测试...\n');

    try {
        // 设置用户
        if (!await setupUser()) {
            throw new Error('用户设置失败');
        }

        // 测试订阅信息
        if (!await testSubscriptionInfo()) {
            throw new Error('订阅信息测试失败');
        }

        // 测试用户信息字段处理
        if (!await testUserInfoHandling()) {
            throw new Error('用户信息字段处理测试失败');
        }

        // 测试空值处理场景
        if (!await testEmptyFieldHandling()) {
            throw new Error('空值处理测试失败');
        }

        console.log('\n=== 测试结果汇总 ===');
        console.log('✅ Settings页面加载功能测试完成');
        console.log('✅ 用户信息字段处理正常');
        console.log('✅ 订阅信息获取正常');
        console.log('✅ 空值处理机制工作正常');
        console.log('✅ 所有字段访问都有安全的默认值');

    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runTest().catch(console.error);
}

module.exports = {
    runTest,
    setupUser,
    testSubscriptionInfo,
    testUserInfoHandling,
    testEmptyFieldHandling
};
