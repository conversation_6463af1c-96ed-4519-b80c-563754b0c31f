/**
 * 时区显示功能测试
 * 
 * 测试日历时区显示是否正确，特别是当前日期的标识
 */

const http = require('http');
const { URL } = require('url');

// 测试配置
const TEST_CONFIG = {
    API_BASE_URL: 'http://localhost:8000',
    TEST_USER: {
        username: 'test',
        password: 'test123'
    }
};

/**
 * 模拟HTTP请求 - 使用内置http模块
 */
async function makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
        const parsedUrl = new URL(url);
        const postData = options.body || '';
        
        const requestOptions = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || 8000,
            path: parsedUrl.pathname + (parsedUrl.search || ''),
            method: options.method || 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                ...options.headers
            }
        };
        
        console.log(`Making request to: ${url}`);
        console.log(`Method: ${requestOptions.method}`);
        
        const req = http.request(requestOptions, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    console.log(`Response status: ${res.statusCode}`);
                    resolve({ 
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode }, 
                        data: jsonData 
                    });
                } catch (e) {
                    console.log(`Response status: ${res.statusCode}`);
                    console.log(`Response data (raw):`, data);
                    resolve({ 
                        response: { ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode }, 
                        data: { error: 'Invalid JSON response', raw: data } 
                    });
                }
            });
        });
        
        req.on('error', (err) => {
            console.error('Request error:', err.message);
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

/**
 * 用户登录
 */
async function loginUser() {
    try {
        const { response, data } = await makeRequest(`${TEST_CONFIG.API_BASE_URL}/api/users/login/`, {
            method: 'POST',
            body: JSON.stringify(TEST_CONFIG.TEST_USER)
        });

        if (response.ok && data.access) {
            console.log('✅ 登录成功');
            return data.access;
        } else {
            console.log('❌ 登录失败:', data);
            return null;
        }
    } catch (error) {
        console.log('❌ 登录请求失败:', error.message);
        return null;
    }
}

/**
 * 获取日历数据并验证时区
 */
async function testTimezoneDisplay(token) {
    try {
        const now = new Date();
        const year = now.getFullYear();
        const month = now.getMonth() + 1;
        const monthStr = `${year}-${String(month).padStart(2, '0')}`;
        
        console.log(`\n=== 测试时区显示 ===`);
        console.log(`当前本地时间: ${now.toLocaleString()}`);
        console.log(`当前UTC时间: ${now.toISOString()}`);
        console.log(`时区偏移: ${now.getTimezoneOffset()} 分钟`);
        
        // 获取当前月份的日历数据
        const { response, data } = await makeRequest(
            `${TEST_CONFIG.API_BASE_URL}/api/calendar/?month=${monthStr}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        );

        if (response.ok) {
            console.log(`✅ 获取${year}年${month}月日历数据成功`);
            
            // 验证时区相关的数据
            console.log(`\n=== 时区验证 ===`);
            
            // 检查今天的日期
            const today = new Date();
            const todayDateString = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
            console.log(`今天的本地日期字符串: ${todayDateString}`);
            
            // 检查预约时间的时区处理
            if (data.reservations && data.reservations.length > 0) {
                console.log(`\n=== 预约时间验证 ===`);
                data.reservations.forEach((reservation, index) => {
                    const startTime = new Date(reservation.start_time);
                    const endTime = new Date(reservation.end_time);
                    
                    console.log(`预约 ${index + 1}:`);
                    console.log(`  - 开始时间 (UTC): ${reservation.start_time}`);
                    console.log(`  - 开始时间 (本地): ${startTime.toLocaleString()}`);
                    console.log(`  - 结束时间 (UTC): ${reservation.end_time}`);
                    console.log(`  - 结束时间 (本地): ${endTime.toLocaleString()}`);
                    
                    // 检查是否是今天的预约
                    const reservationDate = `${startTime.getFullYear()}-${String(startTime.getMonth() + 1).padStart(2, '0')}-${String(startTime.getDate()).padStart(2, '0')}`;
                    const isToday = reservationDate === todayDateString;
                    console.log(`  - 是否是今天的预约: ${isToday ? '是' : '否'}`);
                    
                    // 检查是否已过期
                    const isExpired = endTime < now;
                    console.log(`  - 是否已过期: ${isExpired ? '是' : '否'}`);
                });
            } else {
                console.log('本月暂无预约');
            }
            
            // 验证查询范围
            console.log(`\n=== 查询范围验证 ===`);
            console.log(`查询开始日期: ${data.query_range.start_date}`);
            console.log(`查询结束日期: ${data.query_range.end_date}`);
            
            return true;
        } else {
            console.log('❌ 获取日历数据失败:', data);
            return false;
        }
    } catch (error) {
        console.log('❌ 时区测试失败:', error.message);
        return false;
    }
}

/**
 * 测试日期比较功能
 */
async function testDateComparison() {
    console.log(`\n=== 测试日期比较功能 ===`);
    
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    
    console.log(`今天: ${today.toLocaleDateString()}`);
    console.log(`昨天: ${yesterday.toLocaleDateString()}`);
    console.log(`明天: ${tomorrow.toLocaleDateString()}`);
    
    // 模拟前端的日期比较逻辑
    const isToday = (date) => {
        const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        return checkDate.getTime() === today.getTime();
    };
    
    const isPastDate = (date) => {
        const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        return checkDate.getTime() < today.getTime();
    };
    
    console.log(`今天是否是今天: ${isToday(now) ? '是' : '否'}`);
    console.log(`昨天是否是过去: ${isPastDate(yesterday) ? '是' : '否'}`);
    console.log(`明天是否是过去: ${isPastDate(tomorrow) ? '是' : '否'}`);
    
    return true;
}

/**
 * 主测试函数
 */
async function runTimezoneTest() {
    console.log('开始时区显示功能测试...\n');

    // 1. 登录
    console.log('=== 测试用户登录 ===');
    const token = await loginUser();
    if (!token) {
        console.log('❌ 登录失败，终止测试');
        return;
    }

    // 2. 测试时区显示
    const timezoneTestResult = await testTimezoneDisplay(token);
    if (!timezoneTestResult) {
        console.log('❌ 时区显示测试失败');
        return;
    }

    // 3. 测试日期比较
    const dateComparisonResult = await testDateComparison();
    if (!dateComparisonResult) {
        console.log('❌ 日期比较测试失败');
        return;
    }

    console.log('\n=== 测试结果汇总 ===');
    console.log('✅ 时区显示功能测试完成');
    console.log('✅ 所有时区相关功能正常工作');
}

// 运行测试
runTimezoneTest().catch(console.error);
