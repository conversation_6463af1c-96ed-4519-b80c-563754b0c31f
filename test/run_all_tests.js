/**
 * 综合测试运行器
 * 
 * 运行所有前端测试，确保所有修复的功能都正常工作
 * 提供详细的测试报告和结果汇总
 */

const { spawn } = require('child_process');
const path = require('path');

// 测试配置
const TEST_SUITES = [
    {
        name: '房间内添加环节按钮修复测试',
        file: 'frontend/test_navigation_flow.py',
        type: 'python',
        description: '验证房间内添加环节按钮使用AddStepModal弹窗的功能'
    },
    {
        name: '登录权限功能修复测试',
        file: 'frontend/auth_subscription_test.js',
        type: 'node',
        description: '验证前端向后端发出权限请求和订阅功能的修复'
    },
    {
        name: '预约界面日历更新修复测试',
        file: 'frontend/calendar_update_test.js',
        type: 'node',
        description: '验证预约后日历及时更新和后端接收更新请求的功能'
    },
    {
        name: '日历时区显示修复测试',
        file: 'frontend/timezone_test.js',
        type: 'node',
        description: '验证日历根据用户时区正确显示当天时间的功能'
    },
    {
        name: '房间状态刷新修复测试',
        file: 'frontend/room_status_refresh_test.js',
        type: 'node',
        description: '验证房间下一环节状态刷新时前端显示内容更新的功能'
    },
    {
        name: 'Settings页面加载修复测试',
        file: 'frontend/settings_page_test.js',
        type: 'node',
        description: '验证settings页面正确处理留空功能并正常加载显示'
    },
    {
        name: '页面响应速度优化测试',
        file: 'frontend/page_response_speed_test.js',
        type: 'node',
        description: '验证主界面页面切换延迟和快速切换竞争问题的解决'
    },
    {
        name: '导航栏布局优化测试',
        file: 'frontend/navigation_layout_test.js',
        type: 'node',
        description: '验证上下导航栏为手机系统显示内容预留足够空间的优化'
    },
    {
        name: '主页面设计升级测试',
        file: 'frontend/main_page_design_test.js',
        type: 'node',
        description: '验证主页面元素、背景颜色和布局设计的改进效果'
    }
];

/**
 * 运行单个测试
 */
function runTest(testSuite) {
    return new Promise((resolve, reject) => {
        console.log(`\n🚀 开始运行: ${testSuite.name}`);
        console.log(`📝 描述: ${testSuite.description}`);
        console.log(`📁 文件: ${testSuite.file}`);
        
        const startTime = Date.now();
        
        let command, args;
        if (testSuite.type === 'python') {
            command = 'python';
            args = [testSuite.file];
        } else {
            command = 'node';
            args = [testSuite.file];
        }
        
        const child = spawn(command, args, {
            cwd: path.join(__dirname),
            stdio: 'pipe'
        });
        
        let stdout = '';
        let stderr = '';
        
        child.stdout.on('data', (data) => {
            stdout += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            stderr += data.toString();
        });
        
        child.on('close', (code) => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            const result = {
                name: testSuite.name,
                file: testSuite.file,
                success: code === 0,
                duration,
                stdout,
                stderr,
                exitCode: code
            };
            
            if (code === 0) {
                console.log(`✅ 测试通过 (${duration}ms)`);
            } else {
                console.log(`❌ 测试失败 (${duration}ms)`);
                console.log(`错误代码: ${code}`);
                if (stderr) {
                    console.log(`错误信息: ${stderr.slice(0, 200)}...`);
                }
            }
            
            resolve(result);
        });
        
        child.on('error', (error) => {
            console.log(`❌ 测试执行错误: ${error.message}`);
            reject(error);
        });
    });
}

/**
 * 生成测试报告
 */
function generateReport(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📊 测试结果汇总报告');
    console.log('='.repeat(80));
    
    const totalTests = results.length;
    const passedTests = results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    
    console.log(`\n📈 总体统计:`);
    console.log(`  - 总测试数: ${totalTests}`);
    console.log(`  - 通过测试: ${passedTests}`);
    console.log(`  - 失败测试: ${failedTests}`);
    console.log(`  - 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log(`  - 总耗时: ${totalDuration}ms (${(totalDuration / 1000).toFixed(1)}s)`);
    
    console.log(`\n📋 详细结果:`);
    results.forEach((result, index) => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        const duration = `${result.duration}ms`;
        console.log(`  ${index + 1}. ${status} ${result.name} (${duration})`);
    });
    
    if (failedTests > 0) {
        console.log(`\n🔍 失败测试详情:`);
        results.filter(r => !r.success).forEach((result, index) => {
            console.log(`\n  ${index + 1}. ${result.name}:`);
            console.log(`     文件: ${result.file}`);
            console.log(`     退出代码: ${result.exitCode}`);
            if (result.stderr) {
                console.log(`     错误信息: ${result.stderr.slice(0, 300)}...`);
            }
        });
    }
    
    console.log(`\n🎯 测试质量评估:`);
    if (passedTests === totalTests) {
        console.log(`  🏆 优秀 - 所有测试都通过了！`);
    } else if (passedTests >= totalTests * 0.8) {
        console.log(`  👍 良好 - 大部分测试通过，需要修复少量问题`);
    } else if (passedTests >= totalTests * 0.6) {
        console.log(`  ⚠️  一般 - 部分测试通过，需要重点关注失败的测试`);
    } else {
        console.log(`  🚨 需要改进 - 多数测试失败，需要全面检查和修复`);
    }
    
    console.log('\n' + '='.repeat(80));
    
    return {
        totalTests,
        passedTests,
        failedTests,
        successRate: (passedTests / totalTests) * 100,
        totalDuration,
        results
    };
}

/**
 * 主测试函数
 */
async function runAllTests() {
    console.log('🎯 开始运行所有前端测试...');
    console.log(`📦 共有 ${TEST_SUITES.length} 个测试套件`);
    
    const results = [];
    
    try {
        for (const testSuite of TEST_SUITES) {
            try {
                const result = await runTest(testSuite);
                results.push(result);
                
                // 在测试之间添加短暂延迟，避免资源竞争
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ 测试 ${testSuite.name} 执行失败:`, error.message);
                results.push({
                    name: testSuite.name,
                    file: testSuite.file,
                    success: false,
                    duration: 0,
                    stdout: '',
                    stderr: error.message,
                    exitCode: -1
                });
            }
        }
        
        // 生成测试报告
        const report = generateReport(results);
        
        // 根据测试结果设置退出代码
        if (report.passedTests === report.totalTests) {
            console.log('\n🎉 所有测试都通过了！项目质量优秀！');
            process.exit(0);
        } else {
            console.log(`\n⚠️  有 ${report.failedTests} 个测试失败，请检查并修复相关问题。`);
            process.exit(1);
        }
        
    } catch (error) {
        console.error('\n❌ 测试运行过程中发生严重错误:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ 测试运行失败:', error.message);
        process.exit(1);
    });
}

module.exports = {
    runAllTests,
    runTest,
    generateReport,
    TEST_SUITES
};
